import React, { useState } from 'react';
import { useDashboard } from '../contexts/DashboardContext';
import { RestaurantDetails } from '../types';
import Icon from './Icon';

type DateFilter = 'today' | 'yesterday' | 'this-week' | 'this-month';
type DashboardView = 'overview' | 'pos' | 'menu' | 'tables' | 'orders' | 'analytics' | 'settings';

interface DashboardOverviewProps {
  onNavigate?: (view: DashboardView) => void;
  restaurantDetails: RestaurantDetails;
}

const DashboardOverview: React.FC<DashboardOverviewProps> = ({ onNavigate, restaurantDetails }) => {
  const { metrics, isLoading, filteredOrders, refreshMetrics } = useDashboard();
  const [selectedDateFilter, setSelectedDateFilter] = useState<DateFilter>('today');
  const [currentDate, setCurrentDate] = useState(new Date());

  const handleDateFilterChange = (filter: DateFilter) => {
    setSelectedDateFilter(filter);
    // Refresh metrics with the new date filter
    refreshMetrics(filter);
  };

  const handleExportCSV = () => {
    const csvData = [
      ['Bill No', 'Bill Date', 'Type', 'Settlement Type', 'Customer Name', 'Customer Phone', 'Subtotal', 'Tax Amount', 'Discount Amount', 'Total Amount', 'Payment Status', 'Table Number']
    ];

    // Add order data
    filteredOrders.forEach(order => {
      const billDate = new Date(order.createdAt).toLocaleDateString();
      const orderType = order.orderType.charAt(0).toUpperCase() + order.orderType.slice(1);
      const settlementType = order.paymentMethod ? order.paymentMethod.toUpperCase() : 'N/A';
      const discountAmount = (order as any).discountAmount || 0;

      csvData.push([
        order.orderNumber,
        billDate,
        orderType,
        settlementType,
        order.customerName || 'N/A',
        order.customerPhone || 'N/A',
        `₹${order.subtotal.toFixed(2)}`,
        `₹${order.taxAmount.toFixed(2)}`,
        `₹${discountAmount.toFixed(2)}`,
        `₹${order.totalAmount.toFixed(2)}`,
        order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1),
        order.tableNumber || 'N/A'
      ]);
    });

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `orders-report-${selectedDateFilter}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const handlePrintReport = () => {
    const ordersTableRows = filteredOrders.map(order => {
      const billDate = new Date(order.createdAt).toLocaleDateString();
      const orderType = order.orderType.charAt(0).toUpperCase() + order.orderType.slice(1);
      const settlementType = order.paymentMethod ? order.paymentMethod.toUpperCase() : 'N/A';
      const discountAmount = (order as any).discountAmount || 0;

      return `
        <tr>
          <td>${order.orderNumber}</td>
          <td>${billDate}</td>
          <td>${orderType}</td>
          <td>${settlementType}</td>
          <td>${order.customerName || 'N/A'}</td>
          <td>${order.customerPhone || 'N/A'}</td>
          <td>₹${order.subtotal.toFixed(2)}</td>
          <td>₹${order.taxAmount.toFixed(2)}</td>
          <td>₹${discountAmount.toFixed(2)}</td>
          <td>₹${order.totalAmount.toFixed(2)}</td>
          <td>${order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}</td>
          <td>${order.tableNumber || 'N/A'}</td>
        </tr>
      `;
    }).join('');

    const printContent = `
      <html>
        <head>
          <title>Orders Report - ${selectedDateFilter}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
            .header { text-align: center; margin-bottom: 30px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .summary { margin-bottom: 20px; display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; }
            .summary-item { border: 1px solid #ddd; padding: 10px; border-radius: 5px; text-align: center; }
            .summary-value { font-size: 18px; font-weight: bold; color: #667eea; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Orders Report</h1>
            <p>Period: ${selectedDateFilter.charAt(0).toUpperCase() + selectedDateFilter.slice(1)}</p>
            <p>Generated on: ${new Date().toLocaleDateString()}</p>
          </div>

          <div class="summary">
            <div class="summary-item">
              <div>Total Sales</div>
              <div class="summary-value">₹${metrics.todaysSales.toFixed(2)}</div>
            </div>
            <div class="summary-item">
              <div>Total Orders</div>
              <div class="summary-value">${metrics.todaysOrders}</div>
            </div>
            <div class="summary-item">
              <div>Average Order Value</div>
              <div class="summary-value">₹${(metrics.todaysSales / metrics.todaysOrders || 0).toFixed(2)}</div>
            </div>
          </div>

          <table>
            <thead>
              <tr>
                <th>Bill No</th>
                <th>Bill Date</th>
                <th>Type</th>
                <th>Settlement</th>
                <th>Customer</th>
                <th>Phone</th>
                <th>Subtotal</th>
                <th>Tax</th>
                <th>Discount</th>
                <th>Total</th>
                <th>Status</th>
                <th>Table</th>
              </tr>
            </thead>
            <tbody>
              ${ordersTableRows}
            </tbody>
          </table>
        </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const formatDateDisplay = () => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return currentDate.toLocaleDateString('en-US', options);
  };

  if (isLoading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Loading dashboard data...</p>
      </div>
    );
  }

  return (
    <div className="dashboard-overview">
      <div className="dashboard-controls">
        <div className="controls-left">
          <h2 className="overview-title">Restaurant performance overview</h2>
        </div>

        <div className="controls-center">
          <div className="date-selector">
            <select
              className="date-dropdown"
              value={selectedDateFilter}
              onChange={(e) => handleDateFilterChange(e.target.value as DateFilter)}
            >
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="this-week">This Week</option>
              <option value="this-month">This Month</option>
            </select>
            <span className="current-date">{formatDateDisplay()}</span>
          </div>
        </div>

        <div className="controls-right">
          <div className="control-actions">
            <button
              className="icon-btn download-btn"
              title="Download CSV Report"
              onClick={handleExportCSV}
            >
              <Icon name="download" size="sm" />
            </button>
            <button
              className="icon-btn print-btn"
              title="Print Report"
              onClick={handlePrintReport}
            >
              <Icon name="receipt" size="sm" />
            </button>
            <button className="refresh-data-btn" onClick={() => refreshMetrics(selectedDateFilter)}>
              <Icon name="loading" size="sm" />
              Refresh Data
            </button>
          </div>
        </div>
      </div>

      <div className="metrics-grid">
        {/* Total Sales */}
        <div className="metric-card sales">
          <div className="card-header">
            <h3>Total Sales</h3>
            <div className="card-icon">
              <Icon name="payment" size="md" />
            </div>
          </div>
          <div className="card-content">
            <div className="metric-value">₹{metrics.todaysSales.toFixed(2)}</div>
            <div className="metric-change">
              {metrics.todaysSales > 0
                ? `From ${metrics.todaysOrders} orders`
                : 'Start taking orders to see sales'
              }
            </div>
            <div className="metric-description">
              Total of dine-in + delivery + takeaway
            </div>
          </div>
        </div>

        <div className="metric-card orders">
          <div className="card-header">
            <h3>Total No of Orders</h3>
            <div className="card-icon">
              <Icon name="receipt" size="md" />
            </div>
          </div>
          <div className="card-content">
            <div className="metric-value">{metrics.todaysOrders}</div>
            <div className="metric-change">
              {metrics.todaysOrders > 0
                ? 'Orders processed successfully'
                : 'Ready to serve customers'
              }
            </div>
          </div>
        </div>

        {/* Dine-in Sales */}
        <div className="metric-card dinein">
          <div className="card-header">
            <h3>Dine-in Sales</h3>
            <div className="card-icon">
              <Icon name="table" size="md" />
            </div>
          </div>
          <div className="card-content">
            <div className="metric-value">₹{metrics.dineInSales.toFixed(2)}</div>
            <div className="metric-change">
              {metrics.dineInOrders > 0
                ? `From ${metrics.dineInOrders} orders`
                : 'No dine-in orders yet'
              }
            </div>
          </div>
        </div>

        {/* Takeaway Sales */}
        <div className="metric-card takeaway">
          <div className="card-header">
            <h3>Takeaway Sales</h3>
            <div className="card-icon">
              <Icon name="takeaway" size="md" />
            </div>
          </div>
          <div className="card-content">
            <div className="metric-value">₹{metrics.takeawaySales.toFixed(2)}</div>
            <div className="metric-change">
              {metrics.takeawayOrders > 0
                ? `From ${metrics.takeawayOrders} orders`
                : 'No takeaway orders yet'
              }
            </div>
          </div>
        </div>

        {/* Delivery Sales */}
        <div className="metric-card delivery">
          <div className="card-header">
            <h3>Delivery Sales</h3>
            <div className="card-icon">
              <Icon name="delivery" size="md" />
            </div>
          </div>
          <div className="card-content">
            <div className="metric-value">₹{metrics.deliverySales.toFixed(2)}</div>
            <div className="metric-change">
              {metrics.deliveryOrders > 0
                ? `From ${metrics.deliveryOrders} orders`
                : 'No delivery orders yet'
              }
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Breakdown */}
      <div className="breakdown-section">
        <div className="breakdown-card">
          <h3>Table Status Breakdown</h3>
          <div className="status-grid">
            <div className="status-item available">
              <span className="status-icon">
                <Icon name="available" size="sm" />
              </span>
              <span className="status-label">Available</span>
              <span className="status-count">{metrics.availableTables}</span>
            </div>
            <div className="status-item occupied">
              <span className="status-icon">
                <Icon name="occupied" size="sm" />
              </span>
              <span className="status-label">Occupied</span>
              <span className="status-count">{metrics.occupiedTables}</span>
            </div>
            <div className="status-item reserved">
              <span className="status-icon">
                <Icon name="reserved" size="sm" />
              </span>
              <span className="status-label">Reserved</span>
              <span className="status-count">{metrics.reservedTables}</span>
            </div>
            <div className="status-item maintenance">
              <span className="status-icon">
                <Icon name="maintenance" size="sm" />
              </span>
              <span className="status-label">Maintenance</span>
              <span className="status-count">{metrics.maintenanceTables}</span>
            </div>
          </div>
        </div>

        <div className="breakdown-card">
          <h3>Quick Actions</h3>
          <div className="quick-actions">
            <button
              className="action-btn menu-btn"
              onClick={() => onNavigate?.('menu')}
              title="Manage restaurant menu items"
            >
              <Icon name="utensils" size="sm" /> Manage Menu
            </button>
            {restaurantDetails.restaurantType === 'Dine-In' && (
              <button
                className="action-btn table-btn"
                onClick={() => onNavigate?.('tables')}
                title="Manage restaurant tables"
              >
                <Icon name="table" size="sm" /> Manage Tables
              </button>
            )}
            <button
              className="action-btn pos-btn"
              onClick={() => onNavigate?.('pos')}
              title="Start Order Taking system"
            >
              <Icon name="shopping-cart" size="sm" /> Take Orders
            </button>
            <button
              className="action-btn settings-btn"
              onClick={() => onNavigate?.('settings')}
              title="Open settings and configuration"
            >
              <Icon name="cog" size="sm" /> Settings
            </button>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="activity-section">
        <h3>System Status</h3>
        <div className="activity-list">
          <div className="activity-item">
            <span className="activity-icon">
              <Icon name="check-circle" size="sm" />
            </span>
            <span className="activity-text">System is ready for operations</span>
            <span className="activity-time">Now</span>
          </div>
          {metrics.totalMenuItems === 0 && (
            <div className="activity-item warning">
              <span className="activity-icon">
                <Icon name="warning" size="sm" />
              </span>
              <span className="activity-text">No menu items configured - Add items to start taking orders</span>
              <span className="activity-time">Action needed</span>
            </div>
          )}
          {metrics.totalTables === 0 && (
            <div className="activity-item warning">
              <span className="activity-icon">
                <Icon name="warning" size="sm" />
              </span>
              <span className="activity-text">No tables configured - Set up tables for dine-in service</span>
              <span className="activity-time">Action needed</span>
            </div>
          )}
          {metrics.totalTaxRates === 0 && (
            <div className="activity-item info">
              <span className="activity-icon">
                <Icon name="warning" size="sm" />
              </span>
              <span className="activity-text">No tax rates configured - Configure taxes in settings</span>
              <span className="activity-time">Optional</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;
