import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { MenuItem, Table, TaxRate, Order } from '../types';
import { eventBus, EVENTS } from '../utils/eventBus';

interface DashboardMetrics {
  totalMenuItems: number;
  availableMenuItems: number;
  totalTables: number;
  availableTables: number;
  occupiedTables: number;
  reservedTables: number;
  maintenanceTables: number;
  totalTaxRates: number;
  activeTaxRates: number;
  todaysSales: number;
  todaysOrders: number;
  averageOrderValue: number;
  dineInSales: number;
  takeawaySales: number;
  deliverySales: number;
  dineInOrders: number;
  takeawayOrders: number;
  deliveryOrders: number;
}

interface DashboardContextType {
  metrics: DashboardMetrics;
  isLoading: boolean;
  filteredOrders: Order[];
  refreshMetrics: (dateFilter?: string) => Promise<void>;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

interface DashboardProviderProps {
  children: ReactNode;
  restaurantId: string;
}

export const DashboardProvider: React.FC<DashboardProviderProps> = ({ children, restaurantId }) => {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalMenuItems: 0,
    availableMenuItems: 0,
    totalTables: 0,
    availableTables: 0,
    occupiedTables: 0,
    reservedTables: 0,
    maintenanceTables: 0,
    totalTaxRates: 0,
    activeTaxRates: 0,
    todaysSales: 0,
    todaysOrders: 0,
    averageOrderValue: 0,
    dineInSales: 0,
    takeawaySales: 0,
    deliverySales: 0,
    dineInOrders: 0,
    takeawayOrders: 0,
    deliveryOrders: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);

  const getDateRange = (filter: string) => {
    const now = new Date();
    let startDate: Date;
    let endDate: Date = new Date(now);

    switch (filter) {
      case 'yesterday':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 1);
        endDate = new Date(startDate);
        break;
      case 'this-week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - now.getDay());
        break;
      case 'this-month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'today':
      default:
        startDate = new Date(now);
        break;
    }

    // Set time to start/end of day
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);

    return { startDate, endDate };
  };

  const refreshMetrics = async (dateFilter: string = 'today') => {
    try {
      setIsLoading(true);

      // Fetch all data in parallel
      const [menuItems, tables, taxRates, orders] = await Promise.all([
        window.electronAPI.getMenuItems(restaurantId),
        window.electronAPI.getTables(restaurantId),
        window.electronAPI.getTaxRates(restaurantId),
        (window.electronAPI as any).getOrders(restaurantId)
      ]);

      // Calculate menu metrics
      const totalMenuItems = menuItems.length;
      const availableMenuItems = menuItems.filter(item => item.available).length;

      // Calculate table metrics
      const totalTables = tables.length;
      const availableTables = tables.filter(table => table.status === 'available').length;
      const occupiedTables = tables.filter(table => table.status === 'occupied').length;
      const reservedTables = tables.filter(table => table.status === 'reserved').length;
      const maintenanceTables = tables.filter(table => table.status === 'maintenance').length;

      // Calculate tax metrics
      const totalTaxRates = taxRates.length;
      const activeTaxRates = taxRates.filter(tax => tax.isActive).length;

      // Calculate sales data based on date filter
      const { startDate, endDate } = getDateRange(dateFilter);

      const filteredOrders = orders.filter((order: Order) => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= startDate && orderDate <= endDate;
      });

      const completedOrders = filteredOrders.filter((order: Order) =>
        order.paymentStatus === 'paid'
      );

      const totalSales = completedOrders.reduce((total: number, order: Order) =>
        total + order.totalAmount, 0
      );

      const averageOrderValue = completedOrders.length > 0
        ? totalSales / completedOrders.length
        : 0;

      // Calculate sales by order type
      const dineInOrders = filteredOrders.filter((order: Order) => order.orderType === 'dine-in');
      const takeawayOrders = filteredOrders.filter((order: Order) => order.orderType === 'takeaway');
      const deliveryOrders = filteredOrders.filter((order: Order) => order.orderType === 'delivery');

      const dineInCompletedOrders = dineInOrders.filter((order: Order) => order.paymentStatus === 'paid');
      const takeawayCompletedOrders = takeawayOrders.filter((order: Order) => order.paymentStatus === 'paid');
      const deliveryCompletedOrders = deliveryOrders.filter((order: Order) => order.paymentStatus === 'paid');

      const dineInSales = dineInCompletedOrders.reduce((total: number, order: Order) => total + order.totalAmount, 0);
      const takeawaySales = takeawayCompletedOrders.reduce((total: number, order: Order) => total + order.totalAmount, 0);
      const deliverySales = deliveryCompletedOrders.reduce((total: number, order: Order) => total + order.totalAmount, 0);

      // Store filtered orders for export/print functionality
      setFilteredOrders(filteredOrders);

      setMetrics({
        totalMenuItems,
        availableMenuItems,
        totalTables,
        availableTables,
        occupiedTables,
        reservedTables,
        maintenanceTables,
        totalTaxRates,
        activeTaxRates,
        todaysSales: totalSales,
        todaysOrders: filteredOrders.length,
        averageOrderValue,
        dineInSales,
        takeawaySales,
        deliverySales,
        dineInOrders: dineInOrders.length,
        takeawayOrders: takeawayOrders.length,
        deliveryOrders: deliveryOrders.length,
      });
    } catch (error) {
      console.error('Failed to load dashboard metrics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (restaurantId) {
      refreshMetrics();

      // Set up auto-refresh every 30 seconds
      const interval = setInterval(() => {
        refreshMetrics();
      }, 30000);

      // Listen for events that should trigger dashboard refresh
      const handleRefresh = () => {
        refreshMetrics();
      };

      eventBus.on(EVENTS.ORDER_CREATED, handleRefresh);
      eventBus.on(EVENTS.ORDER_UPDATED, handleRefresh);
      eventBus.on(EVENTS.PAYMENT_COMPLETED, handleRefresh);
      eventBus.on(EVENTS.TABLE_UPDATED, handleRefresh);
      eventBus.on(EVENTS.MENU_UPDATED, handleRefresh);
      eventBus.on(EVENTS.DASHBOARD_REFRESH, handleRefresh);

      return () => {
        clearInterval(interval);
        eventBus.off(EVENTS.ORDER_CREATED, handleRefresh);
        eventBus.off(EVENTS.ORDER_UPDATED, handleRefresh);
        eventBus.off(EVENTS.PAYMENT_COMPLETED, handleRefresh);
        eventBus.off(EVENTS.TABLE_UPDATED, handleRefresh);
        eventBus.off(EVENTS.MENU_UPDATED, handleRefresh);
        eventBus.off(EVENTS.DASHBOARD_REFRESH, handleRefresh);
      };
    }
  }, [restaurantId]);

  const value: DashboardContextType = {
    metrics,
    isLoading,
    filteredOrders,
    refreshMetrics,
  };

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};

export default DashboardContext;
