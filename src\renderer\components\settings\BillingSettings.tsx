import React, { useState, useEffect } from 'react';
import { BillingSettings as BillingSettingsType } from '../../types';
import ImageUpload from '../ImageUpload';

interface BillingSettingsProps {
  restaurantId: string;
}

const BillingSettings: React.FC<BillingSettingsProps> = ({ restaurantId }) => {
  const [settings, setSettings] = useState<BillingSettingsType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadSettings();
  }, [restaurantId]);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const data = await window.electronAPI.getBillingSettings(restaurantId);
      
      if (data) {
        setSettings(data);
      } else {
        // Set default settings
        setSettings({
          id: '',
          restaurantId,
          header: {
            showLogo: false,
            logoUrl: '',
            restaurantName: '',
            address: '',
            phone: '',
            email: '',
            website: '',
            gstNumber: '',
            customText: '',
          },
          footer: {
            thankYouMessage: 'Thank you for your visit!',
            termsAndConditions: '',
            customText: '',
            showQRCode: false,
            qrCodeData: '',
          },
          format: {
            paperSize: 'thermal_80mm',
            fontSize: 'medium',
            showItemImages: false,
            showTaxBreakdown: true,
          },
          printer: {
            printerName: '',
            autoprint: false,
            copies: 1,
          },
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Failed to load billing settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    try {
      setIsSaving(true);
      const result = await window.electronAPI.saveBillingSettings(settings);
      
      if (result.success) {
        setSettings(result.settings!);
        alert('Billing settings saved successfully!');
      } else {
        alert('Failed to save billing settings: ' + result.error);
      }
    } catch (error) {
      console.error('Error saving billing settings:', error);
      alert('Failed to save billing settings');
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (path: string, value: any) => {
    if (!settings) return;

    const pathArray = path.split('.');
    const newSettings = { ...settings };
    let current: any = newSettings;

    for (let i = 0; i < pathArray.length - 1; i++) {
      current = current[pathArray[i]];
    }
    current[pathArray[pathArray.length - 1]] = value;

    setSettings(newSettings);
  };

  if (isLoading) {
    return (
      <div className="settings-loading">
        <div className="loading-spinner"></div>
        <p>Loading billing settings...</p>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="settings-error">
        <p>Failed to load billing settings</p>
        <button onClick={loadSettings} className="btn btn-primary">
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="billing-settings">
      <div className="settings-section">
        <h3 className="section-title">Receipt Header</h3>
        
        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.header.showLogo}
              onChange={(e) => updateSetting('header.showLogo', e.target.checked)}
            />
            <div className="checkbox-content">
              <span className="checkbox-text">Show logo on receipts</span>
            </div>
          </label>
        </div>

        {settings.header.showLogo && (
          <div className="form-group">
            <ImageUpload
              currentImage={settings.header.logoUrl}
              onImageChange={(imageUrl) => updateSetting('header.logoUrl', imageUrl)}
              onImageRemove={() => updateSetting('header.logoUrl', '')}
            />
          </div>
        )}

        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Restaurant Name</label>
            <input
              type="text"
              className="form-input"
              value={settings.header.restaurantName}
              onChange={(e) => updateSetting('header.restaurantName', e.target.value)}
              placeholder="Enter restaurant name"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Phone Number</label>
            <input
              type="tel"
              className="form-input"
              value={settings.header.phone}
              onChange={(e) => updateSetting('header.phone', e.target.value)}
              placeholder="Enter phone number"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Email</label>
            <input
              type="email"
              className="form-input"
              value={settings.header.email}
              onChange={(e) => updateSetting('header.email', e.target.value)}
              placeholder="Enter email address"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Website</label>
            <input
              type="url"
              className="form-input"
              value={settings.header.website}
              onChange={(e) => updateSetting('header.website', e.target.value)}
              placeholder="Enter website URL"
            />
          </div>

          <div className="form-group">
            <label className="form-label">GST Number</label>
            <input
              type="text"
              className="form-input"
              value={settings.header.gstNumber}
              onChange={(e) => updateSetting('header.gstNumber', e.target.value)}
              placeholder="Enter GST number"
            />
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">Address</label>
          <textarea
            className="form-input"
            rows={3}
            value={settings.header.address}
            onChange={(e) => updateSetting('header.address', e.target.value)}
            placeholder="Enter complete address"
          />
        </div>

        <div className="form-group">
          <label className="form-label">Custom Header Text</label>
          <textarea
            className="form-input"
            rows={2}
            value={settings.header.customText}
            onChange={(e) => updateSetting('header.customText', e.target.value)}
            placeholder="Additional text to show in header"
          />
        </div>
      </div>

      <div className="settings-section">
        <h3 className="section-title">Receipt Footer</h3>
        
        <div className="form-group">
          <label className="form-label">Thank You Message</label>
          <input
            type="text"
            className="form-input"
            value={settings.footer.thankYouMessage}
            onChange={(e) => updateSetting('footer.thankYouMessage', e.target.value)}
            placeholder="Thank you message"
          />
        </div>

        <div className="form-group">
          <label className="form-label">Terms and Conditions</label>
          <textarea
            className="form-input"
            rows={3}
            value={settings.footer.termsAndConditions}
            onChange={(e) => updateSetting('footer.termsAndConditions', e.target.value)}
            placeholder="Terms and conditions text"
          />
        </div>

        <div className="form-group">
          <label className="form-label">Custom Footer Text</label>
          <textarea
            className="form-input"
            rows={2}
            value={settings.footer.customText}
            onChange={(e) => updateSetting('footer.customText', e.target.value)}
            placeholder="Additional text to show in footer"
          />
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.footer.showQRCode}
              onChange={(e) => updateSetting('footer.showQRCode', e.target.checked)}
            />
            <span className="checkbox-text">Show QR Code</span>
          </label>
        </div>

        {settings.footer.showQRCode && (
          <div className="form-group">
            <label className="form-label">QR Code Data</label>
            <input
              type="text"
              className="form-input"
              value={settings.footer.qrCodeData}
              onChange={(e) => updateSetting('footer.qrCodeData', e.target.value)}
              placeholder="URL or text for QR code"
            />
          </div>
        )}
      </div>

      <div className="settings-section">
        <h3 className="section-title">Receipt Format</h3>
        
        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Paper Size</label>
            <select
              className="form-select"
              value={settings.format.paperSize}
              onChange={(e) => updateSetting('format.paperSize', e.target.value)}
            >
              <option value="thermal_58mm">Thermal 58mm</option>
              <option value="thermal_80mm">Thermal 80mm</option>
              <option value="A4">A4 Paper</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Font Size</label>
            <select
              className="form-select"
              value={settings.format.fontSize}
              onChange={(e) => updateSetting('format.fontSize', e.target.value)}
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.format.showItemImages}
              onChange={(e) => updateSetting('format.showItemImages', e.target.checked)}
            />
            <span className="checkbox-text">Show item images on receipt</span>
          </label>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.format.showTaxBreakdown}
              onChange={(e) => updateSetting('format.showTaxBreakdown', e.target.checked)}
            />
            <span className="checkbox-text">Show detailed tax breakdown</span>
          </label>
        </div>
      </div>

      <div className="settings-actions">
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="btn btn-primary"
        >
          {isSaving ? 'Saving...' : 'Save Billing Settings'}
        </button>
      </div>
    </div>
  );
};

export default BillingSettings;
