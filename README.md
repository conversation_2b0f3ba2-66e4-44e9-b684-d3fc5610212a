# 🍽️ Zyka POS - Internal Development Repository

**⚠️ INTERNAL USE ONLY - Development Team & Testers**

This is the private development repository for Zyka POS restaurant management system. This repository is exclusively for our internal development team and authorized testers.

## 🛠️ Tech Stack
- **Electron 37.2.3** - Desktop framework
- **TypeScript 5.8.3** - Type-safe development
- **React 19.1.0** - UI framework
- **SQLite3** - Embedded database
- **Node.js** - Runtime environment

## 🚀 Development Setup

### Prerequisites
- **Node.js** v18+
- **npm** or **yarn**
- **Git**

### Setup Instructions

1. **Clone and install**
   ```bash
   git clone [internal-repo-url]
   cd zyka-pos
   npm install
   ```

2. **Environment setup**
   ```bash
   cp .env.example .env
   # Configure your local environment variables
   ```

3. **Development mode**
   ```bash
   npm run dev          # Start webpack dev server
   npm run electron:dev # Launch Electron in dev mode
   ```

4. **Production build**
   ```bash
   npm run build        # Build for production
   npm run dist         # Create distribution packages
   ```

## 📁 Project Structure

```
src/
├── main.ts                    # Electron main process
├── preload.ts                # IPC preload script
├── renderer/                 # React frontend
│   ├── components/           # UI components
│   ├── pages/               # Application pages
│   ├── contexts/            # React contexts
│   ├── hooks/               # Custom hooks
│   ├── services/            # Frontend services
│   ├── types/               # TypeScript types
│   └── utils/               # Utility functions
├── services/                # Backend services
│   ├── databaseService.ts   # Database operations
│   ├── ipcHandlers.ts      # IPC handlers
│   ├── sqliteService.ts    # SQLite service
│   ├── backupService.ts    # Backup/restore
│   └── notificationService.ts # Notifications
└── types/                   # Shared types

Key directories:
├── docs/                    # Internal documentation
├── scripts/                 # Build scripts
├── assets/                  # Application assets
└── dist/                    # Build output
```

## 🛠️ Development Commands

| Command | Description |
|---------|-------------|
| `npm run dev` | Start webpack dev server with hot reload |
| `npm run electron:dev` | Launch Electron in development mode |
| `npm run build` | Build production version |
| `npm run build:dev` | Build development version |
| `npm start` | Start the built application |
| `npm run pack` | Package app (no installer) |
| `npm run dist` | Create distribution packages |
| `npm run test:analytics` | Test analytics functionality |

## �️ Database Schema

Key tables:
- **users** - User authentication and profiles
- **restaurants** - Restaurant information
- **tables** - Table management and status
- **menu_items** - Menu items and categories
- **orders** - Order processing and history
- **order_items** - Order line items
- **tax_rates** - Tax configuration
- **notifications** - Notification system
- **notification_preferences** - User notification settings

## � Environment Configuration

Create `.env` file:
```env
# Database
DB_PATH=./data/zyka.db

# Development
NODE_ENV=development
DEBUG=true

# Analytics (Optional)
GA_MEASUREMENT_ID=your_measurement_id
GA_API_SECRET=your_api_secret

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=your_email
SMTP_PASS=your_password
```

## 📚 Internal Documentation

- [Development Guide](docs/development.md) - Detailed development setup and workflow
- [API Documentation](docs/api-docs.md) - IPC handlers and database operations
- [Project Structure](docs/project-structure.md) - Codebase architecture
- [Analytics Setup](docs/analytics-setup-guide.md) - Analytics configuration

## 🧪 Testing & QA

### Testing Procedures
```bash
# Test analytics functionality
npm run test:analytics

# Build verification
npm run build
npm start

# Development testing
npm run electron:dev
```

### QA Guidelines
- Test all major user flows (POS, table management, orders)
- Verify database operations and data integrity
- Test notification system functionality
- Validate analytics data accuracy
- Check responsive design on different screen sizes

## � Recent Development Updates

### **Latest Features (Internal)**
- ✅ **Comprehensive Notification System** - Real-time notifications for orders, tables, bills, menu, tax, and system events
- ✅ **Analytics Data Integration** - Fixed analytics queries and real-time data display
- ✅ **Database Schema Improvements** - Enhanced foreign key relationships and data integrity
- ✅ **UI/UX Enhancements** - Improved terminology (seats → pax) and streamlined navigation
- ✅ **Bill Processing Fix** - Resolved bill preview display issues
- ✅ **TypeScript Compliance** - All build errors resolved, full type safety

### **Development Priorities**
- [ ] Performance optimization for large datasets
- [ ] Enhanced error handling and logging
- [ ] Automated testing suite expansion
- [ ] Code documentation improvements
- [ ] Security audit and improvements

## 🔧 Troubleshooting

### Common Development Issues
- **Build errors**: Check Node.js version (v18+) and clear `node_modules`
- **Database issues**: Verify SQLite file permissions and backup data
- **IPC communication**: Check preload script and main process handlers
- **Hot reload not working**: Restart webpack dev server

### Internal Support
- **Development questions**: Contact the development team lead
- **Bug reports**: Use internal issue tracking system
- **Feature requests**: Discuss with product team first

---

**🔒 CONFIDENTIAL - Internal Development Team Only**
