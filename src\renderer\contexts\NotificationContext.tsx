import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { Notification, NotificationPreference, ToastNotification, NotificationType } from '../types';

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  preferences: NotificationPreference[];
  toasts: ToastNotification[];
  isLoading: boolean;
  error: string | null;
}

type NotificationAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_NOTIFICATIONS'; payload: Notification[] }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'UPDATE_NOTIFICATION'; payload: { id: string; updates: Partial<Notification> } }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'CLEAR_NOTIFICATIONS' }
  | { type: 'SET_UNREAD_COUNT'; payload: number }
  | { type: 'SET_PREFERENCES'; payload: NotificationPreference[] }
  | { type: 'UPDATE_PREFERENCE'; payload: { category: NotificationType; updates: Partial<NotificationPreference> } }
  | { type: 'ADD_TOAST'; payload: ToastNotification }
  | { type: 'REMOVE_TOAST'; payload: string };

const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  preferences: [],
  toasts: [],
  isLoading: false,
  error: null,
};

function notificationReducer(state: NotificationState, action: NotificationAction): NotificationState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_NOTIFICATIONS':
      return { 
        ...state, 
        notifications: action.payload,
        unreadCount: action.payload.filter(n => !n.readStatus).length,
        isLoading: false 
      };
    
    case 'ADD_NOTIFICATION':
      const newNotifications = [action.payload, ...state.notifications];
      return {
        ...state,
        notifications: newNotifications,
        unreadCount: newNotifications.filter(n => !n.readStatus).length,
      };
    
    case 'UPDATE_NOTIFICATION':
      const updatedNotifications = state.notifications.map(n =>
        n.id === action.payload.id ? { ...n, ...action.payload.updates } : n
      );
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.readStatus).length,
      };
    
    case 'REMOVE_NOTIFICATION':
      const filteredNotifications = state.notifications.filter(n => n.id !== action.payload);
      return {
        ...state,
        notifications: filteredNotifications,
        unreadCount: filteredNotifications.filter(n => !n.readStatus).length,
      };
    
    case 'CLEAR_NOTIFICATIONS':
      return {
        ...state,
        notifications: [],
        unreadCount: 0,
      };
    
    case 'SET_UNREAD_COUNT':
      return { ...state, unreadCount: action.payload };
    
    case 'SET_PREFERENCES':
      return { ...state, preferences: action.payload };
    
    case 'UPDATE_PREFERENCE':
      return {
        ...state,
        preferences: state.preferences.map(p =>
          p.category === action.payload.category ? { ...p, ...action.payload.updates } : p
        ),
      };
    
    case 'ADD_TOAST':
      return {
        ...state,
        toasts: [...state.toasts, action.payload],
      };
    
    case 'REMOVE_TOAST':
      return {
        ...state,
        toasts: state.toasts.filter(t => t.id !== action.payload),
      };
    
    default:
      return state;
  }
}

interface NotificationContextType {
  state: NotificationState;
  // Notification methods
  loadNotifications: (userId: string, filters?: { type?: NotificationType; unreadOnly?: boolean; limit?: number }) => Promise<void>;
  createNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: (userId: string) => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  clearAllNotifications: (userId: string) => Promise<void>;
  // Preference methods
  loadPreferences: (userId: string) => Promise<void>;
  updatePreference: (userId: string, category: NotificationType, preferences: Partial<NotificationPreference>) => Promise<void>;
  // Toast methods
  showToast: (toast: Omit<ToastNotification, 'id'>) => void;
  removeToast: (id: string) => void;
  // Utility methods
  getPreference: (category: NotificationType) => NotificationPreference | undefined;
  isNotificationEnabled: (category: NotificationType) => boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
  userId: string;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children, userId }) => {
  const [state, dispatch] = useReducer(notificationReducer, initialState);

  // Load notifications
  const loadNotifications = useCallback(async (
    userId: string, 
    filters?: { type?: NotificationType; unreadOnly?: boolean; limit?: number }
  ) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const notifications = await window.electronAPI.getNotifications(userId, filters);
      dispatch({ type: 'SET_NOTIFICATIONS', payload: notifications });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load notifications' });
      console.error('Error loading notifications:', error);
    }
  }, []);

  // Create notification
  const createNotification = useCallback(async (notification: Omit<Notification, 'id' | 'createdAt'>) => {
    try {
      const result = await window.electronAPI.createNotification(notification);
      if (result.success && result.notification) {
        dispatch({ type: 'ADD_NOTIFICATION', payload: result.notification });
        
        // Show toast if enabled for this category
        const preference = state.preferences.find(p => p.category === notification.type);
        if (preference?.toastEnabled !== false) {
          showToast({
            type: 'info',
            title: notification.title,
            message: notification.message,
            duration: preference?.autoDismissEnabled ? preference.autoDismissTime * 1000 : 5000,
          });
        }
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to create notification' });
      console.error('Error creating notification:', error);
    }
  }, [state.preferences]);

  // Mark as read
  const markAsRead = useCallback(async (id: string) => {
    try {
      const result = await window.electronAPI.markNotificationAsRead(id);
      if (result.success) {
        dispatch({ 
          type: 'UPDATE_NOTIFICATION', 
          payload: { id, updates: { readStatus: true, readAt: new Date().toISOString() } }
        });
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, []);

  // Mark all as read
  const markAllAsRead = useCallback(async (userId: string) => {
    try {
      const result = await window.electronAPI.markAllNotificationsAsRead(userId);
      if (result.success) {
        const now = new Date().toISOString();
        const updatedNotifications = state.notifications.map(n => ({
          ...n,
          readStatus: true,
          readAt: now,
        }));
        dispatch({ type: 'SET_NOTIFICATIONS', payload: updatedNotifications });
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, [state.notifications]);

  // Delete notification
  const deleteNotification = useCallback(async (id: string) => {
    try {
      const result = await window.electronAPI.deleteNotification(id);
      if (result.success) {
        dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(async (userId: string) => {
    try {
      const result = await window.electronAPI.clearAllNotifications(userId);
      if (result.success) {
        dispatch({ type: 'CLEAR_NOTIFICATIONS' });
      }
    } catch (error) {
      console.error('Error clearing all notifications:', error);
    }
  }, []);

  // Load preferences
  const loadPreferences = useCallback(async (userId: string) => {
    try {
      const preferences = await window.electronAPI.getNotificationPreferences(userId);
      dispatch({ type: 'SET_PREFERENCES', payload: preferences });
    } catch (error) {
      console.error('Error loading notification preferences:', error);
    }
  }, []);

  // Update preference
  const updatePreference = useCallback(async (
    userId: string, 
    category: NotificationType, 
    preferences: Partial<NotificationPreference>
  ) => {
    try {
      const result = await window.electronAPI.updateNotificationPreference(userId, category, preferences);
      if (result.success) {
        dispatch({ type: 'UPDATE_PREFERENCE', payload: { category, updates: preferences } });
      }
    } catch (error) {
      console.error('Error updating notification preference:', error);
    }
  }, []);

  // Show toast
  const showToast = useCallback((toast: Omit<ToastNotification, 'id'>) => {
    const id = `toast_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const toastWithId = { ...toast, id };
    dispatch({ type: 'ADD_TOAST', payload: toastWithId });

    // Auto-remove toast after duration
    const duration = toast.duration || 5000;
    setTimeout(() => {
      dispatch({ type: 'REMOVE_TOAST', payload: id });
    }, duration);
  }, []);

  // Remove toast
  const removeToast = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_TOAST', payload: id });
  }, []);

  // Get preference
  const getPreference = useCallback((category: NotificationType) => {
    return state.preferences.find(p => p.category === category);
  }, [state.preferences]);

  // Check if notification is enabled
  const isNotificationEnabled = useCallback((category: NotificationType) => {
    const preference = getPreference(category);
    return preference?.enabled !== false;
  }, [getPreference]);

  // Initialize data on mount
  useEffect(() => {
    if (userId) {
      loadNotifications(userId);
      loadPreferences(userId);
    }
  }, [userId, loadNotifications, loadPreferences]);

  // Listen for real-time notifications
  useEffect(() => {
    const handleNotificationReceived = (notification: Notification) => {
      if (notification.userId === userId) {
        dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
        
        // Show toast if enabled for this category
        const preference = state.preferences.find(p => p.category === notification.type);
        if (preference?.toastEnabled !== false) {
          showToast({
            type: 'info',
            title: notification.title,
            message: notification.message,
            duration: preference?.autoDismissEnabled ? preference.autoDismissTime * 1000 : 5000,
          });
        }
      }
    };

    window.electronAPI.onNotificationReceived(handleNotificationReceived);

    return () => {
      window.electronAPI.removeNotificationListener();
    };
  }, [userId, state.preferences, showToast]);

  const contextValue: NotificationContextType = {
    state,
    loadNotifications,
    createNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
    loadPreferences,
    updatePreference,
    showToast,
    removeToast,
    getPreference,
    isNotificationEnabled,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};
