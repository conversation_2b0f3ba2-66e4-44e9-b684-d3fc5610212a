# Analytics Setup Guide - Internal Testing

**⚠️ INTERNAL USE ONLY - Development Team & Testers**

This guide helps set up Google Analytics 4 (GA4) integration for testing analytics functionality in development and staging environments.

## Prerequisites

- Google account
- Access to Google Analytics
- Zyka POS system with analytics feature enabled

## Step 1: Create Google Analytics 4 Property

1. **Go to Google Analytics**
   - Visit [analytics.google.com](https://analytics.google.com)
   - Sign in with your Google account

2. **Create a New Property**
   - Click "Admin" (gear icon) in the bottom left
   - Click "Create Property"
   - Choose "GA4" (Google Analytics 4)
   - Enter your business details:
     - Property name: "<PERSON>yk<PERSON> POS - [Your Restaurant Name]"
     - Reporting time zone: Select your local timezone
     - Currency: Select your local currency (e.g., INR for India)

3. **Set Up Data Stream**
   - Choose "Web" as the platform
   - Enter any website URL (can be placeholder like `https://yourrestaurant.com`)
   - Stream name: "Zyka POS Data Stream"
   - Click "Create stream"

## Step 2: Get Your Measurement ID and API Secret

### Get Measurement ID
1. In your GA4 property, go to **Admin > Data Streams**
2. Click on your data stream
3. Copy the **Measurement ID** (starts with "G-")

### Create API Secret
1. In the same data stream settings, scroll down to **Measurement Protocol API secrets**
2. Click "Create"
3. Enter a nickname: "Zyka POS Integration"
4. Click "Create"
5. Copy the **Secret Value** (you won't be able to see it again)

## Step 3: Configure Zyka POS Analytics

1. **Open Zyka POS Settings**
   - Go to Settings > Cloud Analytics
   - Enable "Analytics Sync"

2. **Enter Configuration**
   - **Measurement ID**: Paste the G-XXXXXXXXXX ID from Step 2
   - **API Secret**: Paste the secret value from Step 2
   - **Sync Frequency**: Choose how often to sync (recommended: Every 6 Hours)

3. **Save and Test**
   - Click "Save Configuration"
   - Click "Test Sync Now" to verify the connection

## Step 4: Set Up Custom Dashboards in GA4

### Dashboard 1: Business Overview

1. **Go to Reports > Library**
2. **Create a new report collection**
3. **Add the following reports:**

#### Revenue Analytics
- **Metric**: `purchase` events
- **Dimensions**: `payment_method`, `item_category`
- **Filters**: `event_name = purchase`

#### Subscription Metrics
- **Metric**: `subscription_created`, `subscription_updated`
- **Dimensions**: `plan_type`, `status`
- **Custom metrics**: Subscription conversion rate

#### User Engagement
- **Metric**: `user_registered`, `trial_started`
- **Dimensions**: `subscription_status`, `current_plan`

### Dashboard 2: Restaurant Performance

#### Restaurant Analytics
- **Metric**: `restaurant_created`
- **Dimensions**: `restaurant_type`, `location`

#### Order Analytics
- **Metric**: Custom events from order data
- **Dimensions**: `restaurant_name`, `order_status`

### Dashboard 3: Payment Analytics

#### Payment Success Rate
- **Metric**: `payment_processed`
- **Dimensions**: `status`, `payment_method`
- **Calculated field**: Success rate percentage

#### Revenue Trends
- **Metric**: `purchase` value
- **Dimensions**: Date, `plan_type`
- **Visualization**: Time series chart

## Step 5: Set Up Alerts and Goals

### Revenue Alerts
1. **Go to Admin > Custom Alerts**
2. **Create alert for:**
   - Daily revenue drops below threshold
   - Failed payment rate exceeds 5%
   - New subscription signups

### Conversion Goals
1. **Go to Admin > Conversions**
2. **Mark these events as conversions:**
   - `subscription_created`
   - `trial_started`
   - `purchase`

## Data Being Tracked

### User Events
- `user_registered`: New user signups
- `trial_started`: Trial period begins
- `subscription_activated`: Paid subscription starts

### Restaurant Events
- `restaurant_created`: New restaurant setup
- `restaurant_updated`: Restaurant information changes

### Subscription Events
- `subscription_created`: New subscription
- `subscription_updated`: Plan changes, renewals
- `subscription_cancelled`: Subscription cancellations

### Payment Events
- `payment_processed`: All payment attempts
- `purchase`: Successful payments (revenue tracking)

### Custom Parameters
Each event includes relevant parameters like:
- `user_id`: Unique user identifier
- `plan_type`: basic/premium
- `amount`: Transaction amount
- `currency`: INR/USD/etc.
- `payment_method`: upi/card/netbanking/wallet
- `restaurant_type`: Café/Takeaway/etc.

## Troubleshooting

### Common Issues

1. **"Invalid Measurement ID" Error**
   - Ensure the ID starts with "G-"
   - Check for extra spaces or characters
   - Verify the property is GA4 (not Universal Analytics)

2. **"API Secret Invalid" Error**
   - Regenerate the API secret in GA4
   - Ensure you copied the complete secret value
   - Check that the secret belongs to the correct data stream

3. **No Data Appearing in GA4**
   - Wait 24-48 hours for data to appear
   - Check that sync is enabled and running
   - Verify events are being sent (check sync status)

4. **Sync Failures**
   - Check internet connection
   - Verify GA4 property is active
   - Check error logs in the application

### Data Validation

1. **Real-time Reports**
   - Go to Reports > Realtime in GA4
   - Trigger a test sync
   - Look for custom events appearing

2. **Debug View**
   - Use GA4's DebugView for detailed event inspection
   - Enable debug mode in the analytics configuration

## Best Practices

### Data Privacy
- Ensure compliance with local data protection laws
- Don't send personally identifiable information (PII)
- Use hashed user IDs where possible

### Performance
- Sync during off-peak hours
- Monitor sync performance and adjust frequency
- Keep event payloads minimal

### Monitoring
- Set up regular data quality checks
- Monitor sync success rates
- Review analytics insights weekly

## Advanced Configuration

### Custom Dimensions
Add custom dimensions in GA4 for:
- Restaurant size (small/medium/large)
- Geographic region
- Customer segments

### Enhanced Ecommerce
Configure enhanced ecommerce tracking for:
- Menu item performance
- Average order value
- Customer lifetime value

### Integration with Other Tools
- Connect GA4 to Google Data Studio for advanced reporting
- Set up BigQuery export for raw data analysis
- Integrate with Google Ads for marketing insights

## Support

For technical support with the analytics integration:
1. Check the application logs for error messages
2. Verify your GA4 configuration
3. Test with a minimal dataset first
4. Contact support with specific error messages

## Next Steps

After successful setup:
1. Monitor data flow for the first week
2. Create custom reports based on your business needs
3. Set up automated insights and alerts
4. Train your team on reading the analytics reports
5. Use insights to optimize your restaurant operations

---

**Note**: It may take 24-48 hours for data to appear in Google Analytics after the initial setup. Be patient and ensure your sync is running successfully.
