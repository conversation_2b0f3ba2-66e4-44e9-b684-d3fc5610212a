import * as sqlite3 from 'sqlite3';
import * as path from 'path';
import * as fs from 'fs';
import { app } from 'electron';

// Enable verbose mode for debugging
sqlite3.verbose();

export interface DatabaseConfig {
  dbPath: string;
  backupPath: string;
  enableWAL: boolean;
  enableForeignKeys: boolean;
}

export class SQLiteService {
  private db: sqlite3.Database | null = null;
  private config: DatabaseConfig;
  private isInitialized = false;

  constructor(config?: Partial<DatabaseConfig>) {
    const userDataPath = app.getPath('userData');
    this.config = {
      dbPath: path.join(userDataPath, 'zyka.db'),
      backupPath: path.join(userDataPath, 'backups'),
      enableWAL: true,
      enableForeignKeys: true,
      ...config
    };

    // Ensure backup directory exists
    if (!fs.existsSync(this.config.backupPath)) {
      fs.mkdirSync(this.config.backupPath, { recursive: true });
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.config.dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err);
          reject(err);
          return;
        }

        console.log('Connected to SQLite database:', this.config.dbPath);
        this.setupDatabase()
          .then(() => {
            this.isInitialized = true;
            resolve();
          })
          .catch(reject);
      });
    });
  }

  private async setupDatabase(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      this.db!.serialize(() => {
        // Enable WAL mode for better performance
        if (this.config.enableWAL) {
          this.db!.run("PRAGMA journal_mode = WAL");
        }

        // Enable foreign keys
        if (this.config.enableForeignKeys) {
          this.db!.run("PRAGMA foreign_keys = ON");
        }

        // Set other performance optimizations
        this.db!.run("PRAGMA synchronous = NORMAL");
        this.db!.run("PRAGMA cache_size = 10000");
        this.db!.run("PRAGMA temp_store = MEMORY");

        // Create tables
        this.createTables()
          .then(resolve)
          .catch(reject);
      });
    });
  }

  private async createTables(): Promise<void> {
    const tables = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT UNIQUE NOT NULL,
        pin TEXT NOT NULL,
        full_name TEXT NOT NULL,
        address TEXT NOT NULL,
        phone TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        trial_start_date TEXT,
        trial_end_date TEXT,
        subscription_status TEXT DEFAULT 'trial' CHECK(subscription_status IN ('trial', 'active', 'expired', 'cancelled')),
        current_plan TEXT CHECK(current_plan IN ('basic', 'premium')),
        subscription_end_date TEXT,
        is_active BOOLEAN DEFAULT 1,
        last_login_at TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )`,

      // Restaurants table
      `CREATE TABLE IF NOT EXISTS restaurants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        restaurant_name TEXT NOT NULL,
        restaurant_address TEXT NOT NULL,
        restaurant_type TEXT NOT NULL,
        location TEXT NOT NULL,
        machine_code TEXT UNIQUE NOT NULL,
        phone TEXT,
        email TEXT,
        website TEXT,
        description TEXT,
        gst_number TEXT,
        currency TEXT DEFAULT 'USD',
        currency_symbol TEXT DEFAULT '$',
        timezone TEXT DEFAULT 'UTC',
        tax_rate REAL DEFAULT 0.0,
        service_charge REAL DEFAULT 0.0,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
      )`,

      // Menu items table
      `CREATE TABLE IF NOT EXISTS menu_items (
        id TEXT PRIMARY KEY,
        restaurant_id TEXT NOT NULL,
        name TEXT NOT NULL,
        price REAL NOT NULL CHECK(price >= 0),
        description TEXT,
        category TEXT,
        image TEXT,
        available BOOLEAN DEFAULT 1,
        is_deleted BOOLEAN DEFAULT 0,
        calories INTEGER,
        protein REAL,
        carbs REAL,
        fat REAL,
        allergens TEXT, -- JSON array as string
        preparation_time INTEGER DEFAULT 0, -- in minutes
        popularity INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE
      )`,

      // Tables table
      `CREATE TABLE IF NOT EXISTS tables (
        id TEXT PRIMARY KEY,
        restaurant_id INTEGER NOT NULL,
        table_number TEXT NOT NULL,
        capacity INTEGER NOT NULL CHECK(capacity > 0),
        location TEXT,
        status TEXT DEFAULT 'available' CHECK(status IN ('available', 'occupied', 'reserved', 'maintenance')),
        current_order_id TEXT,
        reservation_customer_name TEXT,
        reservation_customer_phone TEXT,
        reservation_time TEXT,
        reservation_party_size INTEGER,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
        UNIQUE(restaurant_id, table_number)
      )`,

      // Orders table
      `CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        order_number TEXT UNIQUE NOT NULL,
        restaurant_id TEXT NOT NULL,
        table_id TEXT,
        table_number TEXT,
        order_type TEXT NOT NULL CHECK(order_type IN ('dine-in', 'takeaway', 'delivery')),
        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'confirmed', 'preparing', 'ready', 'served', 'completed', 'cancelled')),
        subtotal REAL NOT NULL DEFAULT 0 CHECK(subtotal >= 0),
        tax_amount REAL NOT NULL DEFAULT 0 CHECK(tax_amount >= 0),
        service_charge_amount REAL NOT NULL DEFAULT 0 CHECK(service_charge_amount >= 0),
        discount_amount REAL NOT NULL DEFAULT 0 CHECK(discount_amount >= 0),
        total_amount REAL NOT NULL DEFAULT 0 CHECK(total_amount >= 0),
        payment_status TEXT DEFAULT 'pending' CHECK(payment_status IN ('pending', 'paid', 'refunded', 'partially_paid')),
        payment_method TEXT CHECK(payment_method IN ('cash', 'card', 'upi', 'other')),
        customer_name TEXT,
        customer_phone TEXT,
        customer_email TEXT,
        notes TEXT,
        kot_printed BOOLEAN DEFAULT 0,
        bill_printed BOOLEAN DEFAULT 0,
        order_source TEXT DEFAULT 'pos' CHECK(order_source IN ('pos', 'online', 'phone', 'walk-in')),
        staff_id TEXT,
        device_id TEXT,
        session_id TEXT,
        estimated_delivery_time TEXT,
        actual_delivery_time TEXT,
        rating INTEGER CHECK(rating >= 1 AND rating <= 5),
        feedback TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        completed_at TEXT,
        cancelled_at TEXT,
        FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
        FOREIGN KEY (table_id) REFERENCES tables(id) ON DELETE SET NULL
      )`,

      // Order items table
      `CREATE TABLE IF NOT EXISTS order_items (
        id TEXT PRIMARY KEY,
        order_id TEXT NOT NULL,
        menu_item_id TEXT NOT NULL,
        menu_item_name TEXT NOT NULL,
        menu_item_price REAL NOT NULL CHECK(menu_item_price >= 0),
        quantity INTEGER NOT NULL CHECK(quantity > 0),
        subtotal REAL NOT NULL CHECK(subtotal >= 0),
        notes TEXT,
        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'preparing', 'ready', 'served')),
        modifiers TEXT, -- JSON array as string
        preparation_start_time TEXT,
        preparation_end_time TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
        FOREIGN KEY (menu_item_id) REFERENCES menu_items(id) ON DELETE RESTRICT
      )`,

      // Backups table
      `CREATE TABLE IF NOT EXISTS backups (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        file_path TEXT NOT NULL,
        size INTEGER NOT NULL,
        type TEXT DEFAULT 'manual' CHECK(type IN ('manual', 'automatic', 'scheduled')),
        status TEXT DEFAULT 'completed' CHECK(status IN ('completed', 'failed', 'in_progress')),
        checksum TEXT NOT NULL,
        metadata TEXT, -- JSON as string
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tax rates table
      `CREATE TABLE IF NOT EXISTS tax_rates (
        id TEXT PRIMARY KEY,
        restaurant_id INTEGER NOT NULL,
        tax_name TEXT NOT NULL,
        tax_rate REAL NOT NULL CHECK(tax_rate >= 0),
        tax_type TEXT DEFAULT 'percentage' CHECK(tax_type IN ('percentage', 'fixed')),
        is_default BOOLEAN DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )`,

      // Billing settings table
      `CREATE TABLE IF NOT EXISTS billing_settings (
        id TEXT PRIMARY KEY,
        restaurant_id TEXT NOT NULL,
        header_show_logo BOOLEAN DEFAULT 0,
        header_logo_url TEXT,
        header_restaurant_name TEXT,
        header_address TEXT,
        header_phone TEXT,
        header_email TEXT,
        header_website TEXT,
        header_gst_number TEXT,
        header_custom_text TEXT,
        footer_thank_you_message TEXT DEFAULT 'Thank you for your visit!',
        footer_terms_and_conditions TEXT,
        footer_custom_text TEXT,
        footer_show_qr_code BOOLEAN DEFAULT 0,
        footer_qr_code_data TEXT,
        format_paper_size TEXT DEFAULT 'thermal_80mm',
        format_font_size TEXT DEFAULT 'medium',
        format_show_item_images BOOLEAN DEFAULT 0,
        format_show_tax_breakdown BOOLEAN DEFAULT 1,
        printer_name TEXT,
        printer_autoprint BOOLEAN DEFAULT 0,
        printer_copies INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (restaurant_id) REFERENCES users(user_id) ON DELETE CASCADE
      )`,

      // App settings table
      `CREATE TABLE IF NOT EXISTS app_settings (
        id TEXT PRIMARY KEY,
        restaurant_id TEXT NOT NULL,
        general_currency TEXT DEFAULT 'INR',
        general_currency_symbol TEXT DEFAULT '₹',
        general_language TEXT DEFAULT 'en',
        general_timezone TEXT DEFAULT 'Asia/Kolkata',
        general_date_format TEXT DEFAULT 'DD/MM/YYYY',
        general_time_format TEXT DEFAULT '12h',
        pos_auto_save_orders BOOLEAN DEFAULT 1,
        pos_sound_enabled BOOLEAN DEFAULT 1,
        pos_show_item_images BOOLEAN DEFAULT 1,
        pos_default_tax_rate TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (restaurant_id) REFERENCES users(user_id) ON DELETE CASCADE
      )`,

      // Notifications table
      `CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        restaurant_id INTEGER,
        type TEXT NOT NULL CHECK(type IN ('orders', 'tables', 'bills', 'menu', 'tax', 'system')),
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        data TEXT, -- JSON data for additional context
        read_status BOOLEAN DEFAULT 0,
        priority TEXT DEFAULT 'normal' CHECK(priority IN ('low', 'normal', 'high', 'urgent')),
        auto_dismiss BOOLEAN DEFAULT 0,
        dismiss_after INTEGER DEFAULT 0, -- seconds, 0 means no auto-dismiss
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        read_at TEXT,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE
      )`,

      // Notification preferences table
      `CREATE TABLE IF NOT EXISTS notification_preferences (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        category TEXT NOT NULL CHECK(category IN ('orders', 'tables', 'bills', 'menu', 'tax', 'system')),
        enabled BOOLEAN DEFAULT 1,
        sound_enabled BOOLEAN DEFAULT 1,
        desktop_enabled BOOLEAN DEFAULT 1,
        toast_enabled BOOLEAN DEFAULT 1,
        auto_dismiss_enabled BOOLEAN DEFAULT 1,
        auto_dismiss_time INTEGER DEFAULT 5, -- seconds
        frequency TEXT DEFAULT 'immediate' CHECK(frequency IN ('immediate', 'batched', 'daily')),
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        UNIQUE(user_id, category)
      )`
    ];

    return new Promise((resolve, reject) => {
      let completed = 0;
      const total = tables.length;

      tables.forEach((sql) => {
        this.db!.run(sql, (err) => {
          if (err) {
            console.error('Error creating table:', err);
            reject(err);
            return;
          }
          
          completed++;
          if (completed === total) {
            this.createIndexes()
              .then(resolve)
              .catch(reject);
          }
        });
      });
    });
  }

  private async createIndexes(): Promise<void> {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_user_id ON users(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_restaurants_user_id ON restaurants(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_menu_items_restaurant_id ON menu_items(restaurant_id)',
      'CREATE INDEX IF NOT EXISTS idx_menu_items_category ON menu_items(category)',
      'CREATE INDEX IF NOT EXISTS idx_menu_items_available ON menu_items(available)',
      'CREATE INDEX IF NOT EXISTS idx_tables_restaurant_id ON tables(restaurant_id)',
      'CREATE INDEX IF NOT EXISTS idx_tables_status ON tables(status)',
      'CREATE INDEX IF NOT EXISTS idx_orders_restaurant_id ON orders(restaurant_id)',
      'CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)',
      'CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_orders_table_id ON orders(table_id)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_menu_item_id ON order_items(menu_item_id)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_restaurant_id ON notifications(restaurant_id)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_read_status ON notifications(read_status)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences(user_id)'
    ];

    return new Promise((resolve, reject) => {
      let completed = 0;
      const total = indexes.length;

      indexes.forEach((sql) => {
        this.db!.run(sql, (err) => {
          if (err) {
            console.error('Error creating index:', err);
            reject(err);
            return;
          }
          
          completed++;
          if (completed === total) {
            console.log('Database tables and indexes created successfully');
            this.runMigrations()
              .then(() => resolve())
              .catch(reject);
          }
        });
      });
    });
  }

  private async runMigrations(): Promise<void> {
    try {
      // Check if gst_number column exists in restaurants table
      const tableInfo = await this.all("PRAGMA table_info(restaurants)");
      const hasGstNumber = tableInfo.some((column: any) => column.name === 'gst_number');

      if (!hasGstNumber) {
        console.log('Adding gst_number column to restaurants table...');
        await this.run('ALTER TABLE restaurants ADD COLUMN gst_number TEXT');
        console.log('Successfully added gst_number column');
      }

      // Check if billing_settings table exists and fix foreign key constraint
      const billingTableExists = await this.get("SELECT name FROM sqlite_master WHERE type='table' AND name='billing_settings'");
      if (!billingTableExists) {
        console.log('Creating billing_settings table...');
        await this.run(`CREATE TABLE IF NOT EXISTS billing_settings (
          id TEXT PRIMARY KEY,
          restaurant_id TEXT NOT NULL,
          header_show_logo BOOLEAN DEFAULT 0,
          header_logo_url TEXT,
          header_restaurant_name TEXT,
          header_address TEXT,
          header_phone TEXT,
          header_email TEXT,
          header_website TEXT,
          header_gst_number TEXT,
          header_custom_text TEXT,
          footer_thank_you_message TEXT DEFAULT 'Thank you for your visit!',
          footer_terms_and_conditions TEXT,
          footer_custom_text TEXT,
          footer_show_qr_code BOOLEAN DEFAULT 0,
          footer_qr_code_data TEXT,
          format_paper_size TEXT DEFAULT 'thermal_80mm',
          format_font_size TEXT DEFAULT 'medium',
          format_show_item_images BOOLEAN DEFAULT 0,
          format_show_tax_breakdown BOOLEAN DEFAULT 1,
          printer_name TEXT,
          printer_autoprint BOOLEAN DEFAULT 0,
          printer_copies INTEGER DEFAULT 1,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (restaurant_id) REFERENCES users(user_id) ON DELETE CASCADE
        )`);
        console.log('Successfully created billing_settings table');
      }

      // Check if app_settings table exists
      const appTableExists = await this.get("SELECT name FROM sqlite_master WHERE type='table' AND name='app_settings'");
      if (!appTableExists) {
        console.log('Creating app_settings table...');
        await this.run(`CREATE TABLE IF NOT EXISTS app_settings (
          id TEXT PRIMARY KEY,
          restaurant_id TEXT NOT NULL,
          general_currency TEXT DEFAULT 'INR',
          general_currency_symbol TEXT DEFAULT '₹',
          general_language TEXT DEFAULT 'en',
          general_timezone TEXT DEFAULT 'Asia/Kolkata',
          general_date_format TEXT DEFAULT 'DD/MM/YYYY',
          general_time_format TEXT DEFAULT '12h',
          pos_auto_save_orders BOOLEAN DEFAULT 1,
          pos_sound_enabled BOOLEAN DEFAULT 1,
          pos_show_item_images BOOLEAN DEFAULT 1,
          pos_default_tax_rate TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (restaurant_id) REFERENCES users(user_id) ON DELETE CASCADE
        )`);
        console.log('Successfully created app_settings table');
      }

      // Drop and recreate settings tables with correct foreign key constraints
      try {
        // Check if we need to fix foreign key constraints
        const existingBillingTable = await this.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='billing_settings'");
        if (existingBillingTable && existingBillingTable.sql.includes('REFERENCES restaurants(user_id)')) {
          console.log('Dropping billing_settings table to fix foreign key constraint...');
          await this.run('DROP TABLE IF EXISTS billing_settings');
        }

        const existingAppTable = await this.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='app_settings'");
        if (existingAppTable && existingAppTable.sql.includes('REFERENCES restaurants(user_id)')) {
          console.log('Dropping app_settings table to fix foreign key constraint...');
          await this.run('DROP TABLE IF EXISTS app_settings');
        }
      } catch (fkError) {
        console.log('Foreign key constraint check failed:', fkError instanceof Error ? fkError.message : 'Unknown error');
      }

      // Migration: Fix tax_rates table restaurant_id column type
      try {
        const taxRatesTableInfo = await this.all("PRAGMA table_info(tax_rates)");
        const restaurantIdColumn = taxRatesTableInfo.find((column: any) => column.name === 'restaurant_id');

        if (restaurantIdColumn && restaurantIdColumn.type === 'TEXT') {
          console.log('Migrating tax_rates table to fix restaurant_id column type...');

          // Check if there are any existing tax rates
          const existingTaxRates = await this.all('SELECT * FROM tax_rates');

          // Drop and recreate the tax_rates table with correct schema
          await this.run('DROP TABLE IF EXISTS tax_rates');

          // Recreate with correct schema
          await this.run(`
            CREATE TABLE tax_rates (
              id TEXT PRIMARY KEY,
              restaurant_id INTEGER NOT NULL,
              tax_name TEXT NOT NULL,
              tax_rate REAL NOT NULL CHECK(tax_rate >= 0),
              tax_type TEXT DEFAULT 'percentage' CHECK(tax_type IN ('percentage', 'fixed')),
              is_default BOOLEAN DEFAULT 0,
              is_active BOOLEAN DEFAULT 1,
              created_at TEXT DEFAULT CURRENT_TIMESTAMP,
              updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE
            )
          `);

          // Restore existing tax rates with corrected restaurant_id values
          for (const taxRate of existingTaxRates) {
            try {
              // Try to find the restaurant by user_id first, then by id
              let restaurant = await this.get('SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1', [taxRate.restaurant_id]);

              if (!restaurant && /^\d+$/.test(taxRate.restaurant_id)) {
                // If restaurant_id looks like a number, try to find by id
                restaurant = await this.get('SELECT id FROM restaurants WHERE id = ? AND is_active = 1', [parseInt(taxRate.restaurant_id)]);
              }

              if (restaurant) {
                await this.run(`
                  INSERT INTO tax_rates (
                    id, restaurant_id, tax_name, tax_rate, tax_type, is_default,
                    is_active, created_at, updated_at
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                  taxRate.id,
                  restaurant.id, // Use the correct integer restaurant ID
                  taxRate.tax_name,
                  taxRate.tax_rate,
                  taxRate.tax_type,
                  taxRate.is_default,
                  taxRate.is_active,
                  taxRate.created_at,
                  taxRate.updated_at
                ]);
              } else {
                console.warn(`Could not find restaurant for tax rate ${taxRate.id} with restaurant_id ${taxRate.restaurant_id}`);
              }
            } catch (restoreError) {
              console.error(`Error restoring tax rate ${taxRate.id}:`, restoreError);
            }
          }

          console.log('Successfully migrated tax_rates table');
        }
      } catch (migrationError) {
        console.error('Tax rates migration error:', migrationError);
      }

      // Migration: Fix tables table restaurant_id column type
      try {
        const tablesTableInfo = await this.all("PRAGMA table_info(tables)");
        const restaurantIdColumn = tablesTableInfo.find((column: any) => column.name === 'restaurant_id');

        if (restaurantIdColumn && restaurantIdColumn.type === 'TEXT') {
          console.log('Migrating tables table to fix restaurant_id column type...');

          // Check if there are any existing tables
          const existingTables = await this.all('SELECT * FROM tables');

          // Drop and recreate the tables table with correct schema
          await this.run('DROP TABLE IF EXISTS tables');

          // Recreate with correct schema
          await this.run(`
            CREATE TABLE tables (
              id TEXT PRIMARY KEY,
              restaurant_id INTEGER NOT NULL,
              table_number TEXT NOT NULL,
              capacity INTEGER NOT NULL CHECK(capacity > 0),
              location TEXT,
              status TEXT DEFAULT 'available' CHECK(status IN ('available', 'occupied', 'reserved', 'maintenance')),
              current_order_id TEXT,
              reservation_customer_name TEXT,
              reservation_customer_phone TEXT,
              reservation_time TEXT,
              reservation_party_size INTEGER,
              is_active BOOLEAN DEFAULT 1,
              created_at TEXT DEFAULT CURRENT_TIMESTAMP,
              updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
              UNIQUE(restaurant_id, table_number)
            )
          `);

          // Restore existing tables with corrected restaurant_id values
          for (const table of existingTables) {
            try {
              // Try to find the restaurant by user_id first, then by id
              let restaurant = await this.get('SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1', [table.restaurant_id]);

              if (!restaurant && /^\d+$/.test(table.restaurant_id)) {
                // If restaurant_id looks like a number, try to find by id
                restaurant = await this.get('SELECT id FROM restaurants WHERE id = ? AND is_active = 1', [parseInt(table.restaurant_id)]);
              }

              if (restaurant) {
                await this.run(`
                  INSERT INTO tables (
                    id, restaurant_id, table_number, capacity, location, status,
                    current_order_id, reservation_customer_name, reservation_customer_phone,
                    reservation_time, reservation_party_size, is_active, created_at, updated_at
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                  table.id,
                  restaurant.id, // Use the correct integer restaurant ID
                  table.table_number,
                  table.capacity,
                  table.location,
                  table.status,
                  table.current_order_id,
                  table.reservation_customer_name,
                  table.reservation_customer_phone,
                  table.reservation_time,
                  table.reservation_party_size,
                  table.is_active,
                  table.created_at,
                  table.updated_at
                ]);
              } else {
                console.warn(`Could not find restaurant for table ${table.id} with restaurant_id ${table.restaurant_id}`);
              }
            } catch (restoreError) {
              console.error(`Error restoring table ${table.id}:`, restoreError);
            }
          }

          console.log('Successfully migrated tables table');
        }
      } catch (migrationError) {
        console.error('Tables migration error:', migrationError);
      }

      // Migration: Create default notification preferences for existing users
      try {
        const existingUsers = await this.all('SELECT user_id FROM users WHERE is_active = 1');
        const categories = ['orders', 'tables', 'bills', 'menu', 'tax', 'system'];

        for (const user of existingUsers) {
          for (const category of categories) {
            // Check if preference already exists
            const existingPref = await this.get(
              'SELECT id FROM notification_preferences WHERE user_id = ? AND category = ?',
              [user.user_id, category]
            );

            if (!existingPref) {
              const prefId = `notif_pref_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
              await this.run(`
                INSERT INTO notification_preferences (
                  id, user_id, category, enabled, sound_enabled, desktop_enabled,
                  toast_enabled, auto_dismiss_enabled, auto_dismiss_time, frequency,
                  created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                prefId, user.user_id, category, 1, 1, 1, 1, 1, 5, 'immediate',
                new Date().toISOString(), new Date().toISOString()
              ]);
            }
          }
        }

        console.log('Successfully created default notification preferences');
      } catch (migrationError) {
        console.error('Notification preferences migration error:', migrationError);
      }
    } catch (error) {
      console.error('Migration error:', error);
      // Don't throw error to prevent app from crashing
    }
  }

  // Generic query methods
  async run(sql: string, params: any[] = []): Promise<{ lastID: number; changes: number }> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      this.db!.run(sql, params, function(err) {
        if (err) {
          console.error('SQL Error:', err, 'Query:', sql, 'Params:', params);
          reject(err);
          return;
        }
        resolve({ lastID: this.lastID, changes: this.changes });
      });
    });
  }

  async get<T = any>(sql: string, params: any[] = []): Promise<T | undefined> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      this.db!.get(sql, params, (err, row) => {
        if (err) {
          console.error('SQL Error:', err, 'Query:', sql, 'Params:', params);
          reject(err);
          return;
        }
        resolve(row as T);
      });
    });
  }

  async all<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      this.db!.all(sql, params, (err, rows) => {
        if (err) {
          console.error('SQL Error:', err, 'Query:', sql, 'Params:', params);
          reject(err);
          return;
        }
        resolve(rows as T[]);
      });
    });
  }

  async close(): Promise<void> {
    if (!this.db) return;

    return new Promise((resolve, reject) => {
      this.db!.close((err) => {
        if (err) {
          reject(err);
          return;
        }
        console.log('Database connection closed');
        this.db = null;
        this.isInitialized = false;
        resolve();
      });
    });
  }

  // Backup functionality
  async createBackup(filename?: string): Promise<{ success: boolean; filename?: string; error?: string }> {
    try {
      if (!this.db) throw new Error('Database not initialized');

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFilename = filename || `zyka-backup-${timestamp}.db`;
      const backupPath = path.join(this.config.backupPath, backupFilename);

      // Create backup by copying the database file
      // First, ensure WAL checkpoint to get all data in main db file
      await this.run('PRAGMA wal_checkpoint(FULL)');

      // Copy the database file
      fs.copyFileSync(this.config.dbPath, backupPath);

      // Record backup in metadata
      await this.recordBackup(backupFilename, backupPath);

      return { success: true, filename: backupFilename };
    } catch (error) {
      console.error('Backup failed:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  private async recordBackup(filename: string, filePath: string): Promise<void> {
    const stats = fs.statSync(filePath);
    const checksum = require('crypto').createHash('md5').update(fs.readFileSync(filePath)).digest('hex');

    await this.run(
      `INSERT INTO backups (id, filename, file_path, size, type, status, checksum, metadata, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        `backup_${Date.now()}`,
        filename,
        filePath,
        stats.size,
        'manual',
        'completed',
        checksum,
        JSON.stringify({ version: '1.0', recordCounts: {} }),
        new Date().toISOString()
      ]
    );
  }

  async getBackups(): Promise<any[]> {
    return this.all(`SELECT * FROM backups ORDER BY created_at DESC`);
  }

  async restoreBackup(backupPath: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!fs.existsSync(backupPath)) {
        throw new Error('Backup file not found');
      }

      // Close current connection
      await this.close();

      // Replace current database with backup
      fs.copyFileSync(backupPath, this.config.dbPath);

      // Reinitialize
      await this.initialize();

      return { success: true };
    } catch (error) {
      console.error('Restore failed:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  // Transaction support
  async transaction<T>(callback: () => Promise<T>): Promise<T> {
    if (!this.db) throw new Error('Database not initialized');

    await this.run('BEGIN TRANSACTION');
    try {
      const result = await callback();
      await this.run('COMMIT');
      return result;
    } catch (error) {
      await this.run('ROLLBACK');
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<{ healthy: boolean; details: any }> {
    try {
      if (!this.db) {
        return { healthy: false, details: { error: 'Database not initialized' } };
      }

      // Test basic query
      await this.get('SELECT 1 as test');

      // Get database info
      const userVersion = await this.get('PRAGMA user_version');
      const journalMode = await this.get('PRAGMA journal_mode');
      const foreignKeys = await this.get('PRAGMA foreign_keys');

      return {
        healthy: true,
        details: {
          userVersion: userVersion?.user_version,
          journalMode: journalMode?.journal_mode,
          foreignKeys: foreignKeys?.foreign_keys === 1,
          dbPath: this.config.dbPath,
          initialized: this.isInitialized
        }
      };
    } catch (error) {
      return {
        healthy: false,
        details: { error: (error as Error).message }
      };
    }
  }
}

// Singleton instance
let sqliteService: SQLiteService | null = null;

export function getSQLiteService(): SQLiteService {
  if (!sqliteService) {
    sqliteService = new SQLiteService();
  }
  return sqliteService;
}
