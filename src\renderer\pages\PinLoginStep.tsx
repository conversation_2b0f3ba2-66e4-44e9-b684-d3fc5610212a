import React, { useState, useEffect } from 'react';
import Button from '../components/Button';
import StepIndicator from '../components/StepIndicator';
import { UserDetails, RestaurantDetails } from '../types';
import Icon from '../components/Icon';

interface PinLoginStepProps {
  onComplete: () => void;
  userDetails: UserDetails;
  restaurantDetails?: RestaurantDetails;
  isReturningUser?: boolean;
}

const PinLoginStep: React.FC<PinLoginStepProps> = ({ onComplete, userDetails, restaurantDetails, isReturningUser = false }) => {
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleKeypadPress = (digit: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + digit);
    }
  };

  const handleBackspace = () => {
    setPin(prev => prev.slice(0, -1));
  };

  const handleClear = () => {
    setPin('');
  };

  const handleSubmitClick = () => {
    const fakeEvent = { preventDefault: () => {} } as React.FormEvent;
    handleSubmit(fakeEvent);
  };

  // Add keyboard support for PIN entry
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Only handle keyboard input if not currently loading
      if (isLoading) return;

      const key = event.key;

      // Handle number keys (0-9) from both main keyboard and numpad
      if (/^[0-9]$/.test(key) && pin.length < 6) {
        event.preventDefault();
        setPin(prev => prev + key);
        return;
      }

      // Handle backspace/delete
      if (key === 'Backspace' || key === 'Delete') {
        event.preventDefault();
        setPin(prev => prev.slice(0, -1));
        return;
      }

      // Handle Enter key to submit (if PIN is complete)
      if (key === 'Enter' && pin.length === 6) {
        event.preventDefault();
        handleSubmitClick();
        return;
      }

      // Handle Escape to clear PIN
      if (key === 'Escape') {
        event.preventDefault();
        setPin('');
        return;
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleKeyPress);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [pin, isLoading]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    if (pin.length !== 6) {
      setMessage({ type: 'error', text: 'PIN must be 6 digits' });
      return;
    }

    setIsLoading(true);

    try {
      const result = await window.electronAPI.validatePin({ pin });

      if (result.valid) {
        setMessage({ 
          type: 'success', 
          text: 'Login successful! Welcome to Zyka POS!' 
        });
        
        // Wait a moment to show the success message
        setTimeout(() => {
          onComplete();
        }, 1500);
      } else {
        setMessage({ 
          type: 'error', 
          text: 'Invalid PIN. Please check your email for the correct PIN.' 
        });
      }
    } catch (error) {
      console.error('Error validating PIN:', error);
      setMessage({ 
        type: 'error', 
        text: 'An error occurred. Please try again.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderKeypad = () => {
    const keys = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['clear', '0', 'backspace']
    ];

    return (
      <div className="mobile-keypad">
        {keys.map((row, rowIndex) => (
          <div key={rowIndex} className="keypad-row">
            {row.map((key) => (
              <button
                key={key}
                type="button"
                className={`keypad-btn ${key === 'clear' || key === 'backspace' ? 'keypad-action' : ''}`}
                onClick={() => {
                  if (key === 'clear') handleClear();
                  else if (key === 'backspace') handleBackspace();
                  else handleKeypadPress(key);
                }}
                disabled={isLoading}
              >
                {key === 'backspace' ? (
                  <Icon name="chevron-left" size="md" />
                ) : key === 'clear' ? (
                  'Clear'
                ) : (
                  key
                )}
              </button>
            ))}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="mobile-pin-login">
      <div className="pin-login-container">
        {/* Header with Restaurant Name */}
        <div className="pin-login-header">
          <div className="restaurant-info">
            <h1 className="restaurant-name">
              {restaurantDetails?.restaurantName || 'Restaurant'}
            </h1>
            <p className="login-subtitle">
              Login to {restaurantDetails?.restaurantName || 'your restaurant'}
            </p>
          </div>
        </div>

        {!isReturningUser && <StepIndicator currentStep={2} totalSteps={3} />}

        {/* Message Display */}
        {message && (
          <div className={`pin-message pin-message-${message.type}`}>
            <div className="message-icon">
              {message.type === 'success' ? (
                <Icon name="check-circle" size="sm" />
              ) : (
                <Icon name="warning" size="sm" />
              )}
            </div>
            <span>{message.text}</span>
          </div>
        )}

        {/* User Info */}
        <div className="user-info-section">
          <div className="user-avatar">
            {userDetails.fullName.charAt(0).toUpperCase()}
          </div>
          <div className="user-details">
            <p className="user-id">User ID: {userDetails.userId}</p>
            <p className="user-email">{userDetails.email}</p>
          </div>
        </div>

        {/* PIN Display */}
        <div className="pin-display-section">
          <h2 className="pin-title">Enter Your 6-Digit PIN</h2>
          <div className="pin-dots">
            {Array.from({ length: 6 }, (_, index) => (
              <div key={index} className={`pin-dot ${index < pin.length ? 'filled' : 'empty'}`}>
                {index < pin.length ? '●' : ''}
              </div>
            ))}
          </div>
          <div className="keyboard-help">
            <p>Use your keyboard or tap the keypad below</p>
            <p className="keyboard-shortcuts">
              <span>Numbers: 0-9</span> • <span>Backspace: Delete</span> • <span>Enter: Submit</span> • <span>Esc: Clear</span>
            </p>
          </div>
        </div>

        {/* Mobile Keypad */}
        {renderKeypad()}

        {/* Submit Button */}
        <div className="pin-actions">
          <Button
            type="button"
            variant="primary"
            isLoading={isLoading}
            disabled={isLoading || pin.length !== 6}
            className="pin-submit-btn"
            onClick={handleSubmitClick}
          >
            {isLoading ? (
              <>
                <span className="loading-spinner"></span>
                Verifying...
              </>
            ) : (
              'Access POS System'
            )}
          </Button>
        </div>

        {!isReturningUser && (
          <div className="pin-help">
            <p>Check your email for the 6-digit PIN</p>
          </div>
        )}

        {/* Footer */}
        <div className="pin-login-footer">
          <p>Zyka POS v1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default PinLoginStep;
