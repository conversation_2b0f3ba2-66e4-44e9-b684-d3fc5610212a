import React, { useState, useEffect } from 'react';
import ImageUpload from '../ImageUpload';
import { billingService } from '../../services/billingService';
import { settingsEventBus, SETTINGS_EVENTS, SettingsEventData } from '../../utils/settingsEventBus';
import { BillingSettings } from '../../types';

interface BillingSettingsTabProps {
  restaurantId: string;
}

interface ToastMessage {
  type: 'success' | 'error' | 'info';
  message: string;
}

const BillingSettingsTab: React.FC<BillingSettingsTabProps> = ({ restaurantId }) => {
  const [availablePrinters, setAvailablePrinters] = useState<string[]>([]);
  const [settings, setSettings] = useState<BillingSettings>({
    id: '',
    restaurantId: '',
    header: {
      showLogo: false,
      logoUrl: '',
      restaurantName: '',
      address: '',
      phone: '',
      email: '',
      website: '',
      gstNumber: '',
      customText: '',
    },
    footer: {
      thankYouMessage: 'Thank you for your visit!',
      termsAndConditions: '',
      customText: '',
      showQRCode: false,
      qrCodeData: '',
    },
    format: {
      paperSize: 'thermal_80mm',
      fontSize: 'medium',
      showItemImages: false,
      showTaxBreakdown: true,
    },
    printer: {
      printerName: '',
      autoprint: false,
      copies: 1,
    },
    updatedAt: new Date().toISOString()
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [toast, setToast] = useState<ToastMessage | null>(null);

  // Load settings when component mounts
  useEffect(() => {
    loadSettings();
  }, [restaurantId]);

  // Listen for restaurant details updates to sync fields
  useEffect(() => {
    const handleRestaurantDetailsUpdate = (data: SettingsEventData) => {
      setSettings(prev => ({
        ...prev,
        header: {
          ...prev.header,
          restaurantName: data.restaurantName || prev.header.restaurantName,
          address: data.restaurantAddress || prev.header.address,
          phone: data.phone || prev.header.phone,
          email: data.email || prev.header.email,
          website: data.website || prev.header.website,
          gstNumber: data.gstNumber || prev.header.gstNumber,
        }
      }));
    };

    settingsEventBus.on(SETTINGS_EVENTS.RESTAURANT_DETAILS_UPDATED, handleRestaurantDetailsUpdate);

    return () => {
      settingsEventBus.off(SETTINGS_EVENTS.RESTAURANT_DETAILS_UPDATED, handleRestaurantDetailsUpdate);
    };
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const data = await window.electronAPI.getBillingSettings(restaurantId);

      if (data) {
        setSettings(data);
      } else {
        // Load restaurant details to populate header fields
        const restaurantData = await window.electronAPI.getRestaurantDetails(restaurantId);
        if (restaurantData) {
          setSettings(prev => ({
            ...prev,
            restaurantId: restaurantId,
            header: {
              ...prev.header,
              restaurantName: restaurantData.restaurantName || '',
              address: restaurantData.restaurantAddress || '',
              phone: restaurantData.phone || '',
              email: restaurantData.email || '',
              website: restaurantData.website || '',
              gstNumber: restaurantData.gstNumber || '',
            }
          }));
        }
      }
    } catch (error) {
      console.error('Failed to load billing settings:', error);
      setToast({ type: 'error', message: 'Failed to load billing settings' });
    } finally {
      setIsLoading(false);
    }
  };

  // Detect available printers on component mount
  useEffect(() => {
    const detectPrinters = async () => {
      try {
        // Simulate printer detection - replace with actual implementation
        const printers = [
          'Default Printer',
          'HP LaserJet Pro',
          'Epson Thermal Printer',
          'Canon PIXMA',
          'Brother HL-L2350DW'
        ];
        setAvailablePrinters(printers);

        // Set default printer if none selected
        if (!settings.printer.printerName && printers.length > 0) {
          updateSetting('printer.printerName', printers[0]);
        }
      } catch (error) {
        console.error('Error detecting printers:', error);
        setToast({ type: 'error', message: 'Failed to detect printers' });
      }
    };

    detectPrinters();
  }, []);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const settingsToSave = {
        ...settings,
        id: settings.id || undefined,
        restaurantId: restaurantId,
        updatedAt: new Date().toISOString()
      };

      const result = await window.electronAPI.saveBillingSettings(settingsToSave);

      if (result.success && result.settings) {
        setSettings(result.settings);

        // Refresh billing service with updated settings
        await billingService.loadBillingSettings(restaurantId);

        // Emit event to notify other components
        settingsEventBus.emit(SETTINGS_EVENTS.BILLING_SETTINGS_UPDATED, result.settings);

        setToast({ type: 'success', message: 'Billing settings saved successfully!' });
      } else {
        setToast({ type: 'error', message: `Failed to save billing settings: ${result.error}` });
      }
    } catch (error) {
      console.error('Error saving billing settings:', error);
      setToast({ type: 'error', message: 'Failed to save billing settings' });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePreviewReceipt = () => {
    // Create a sample receipt for preview
    const sampleReceipt = {
      orderNumber: 'ORD-001',
      date: new Date().toLocaleDateString(),
      time: new Date().toLocaleTimeString(),
      items: [
        { name: 'Margherita Pizza', quantity: 1, price: 299, total: 299 },
        { name: 'Coca Cola', quantity: 2, price: 50, total: 100 }
      ],
      subtotal: 399,
      tax: 71.82,
      total: 470.82
    };

    // Generate receipt HTML
    const receiptHTML = generateReceiptHTML(sampleReceipt);

    // Open preview window
    const previewWindow = window.open('', '_blank', 'width=400,height=600,scrollbars=yes');
    if (previewWindow) {
      previewWindow.document.write(receiptHTML);
      previewWindow.document.close();
    }
  };

  const generateReceiptHTML = (receipt: any) => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Receipt Preview</title>
        <style>
          body {
            font-family: 'Courier New', monospace;
            font-size: ${settings.format.fontSize === 'small' ? '12px' : settings.format.fontSize === 'large' ? '16px' : '14px'};
            margin: 20px;
            background: white;
            color: black;
          }
          .receipt {
            max-width: ${settings.format.paperSize === 'thermal_80mm' ? '300px' : settings.format.paperSize === 'thermal_58mm' ? '200px' : '600px'};
            margin: 0 auto;
            border: 1px solid #ccc;
            padding: 20px;
          }
          .header { text-align: center; margin-bottom: 20px; }
          .logo { max-width: 100px; margin-bottom: 10px; }
          .restaurant-name { font-size: 18px; font-weight: bold; margin-bottom: 5px; }
          .address { font-size: 12px; margin-bottom: 3px; }
          .contact { font-size: 12px; margin-bottom: 3px; }
          .divider { border-top: 1px dashed #000; margin: 15px 0; }
          .order-info { margin-bottom: 15px; }
          .items { margin-bottom: 15px; }
          .item { display: flex; justify-content: space-between; margin-bottom: 5px; }
          .totals { margin-bottom: 15px; }
          .total-row { display: flex; justify-content: space-between; margin-bottom: 3px; }
          .grand-total { font-weight: bold; font-size: 16px; border-top: 1px solid #000; padding-top: 5px; }
          .footer { text-align: center; margin-top: 20px; font-size: 12px; }
          .qr-code { text-align: center; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="receipt">
          <div class="header">
            ${settings.header.showLogo && settings.header.logoUrl ? `<img src="${settings.header.logoUrl}" alt="Logo" class="logo">` : ''}
            <div class="restaurant-name">${settings.header.restaurantName || 'Restaurant Name'}</div>
            <div class="address">${settings.header.address || 'Restaurant Address'}</div>
            ${settings.header.phone ? `<div class="contact">Phone: ${settings.header.phone}</div>` : ''}
            ${settings.header.email ? `<div class="contact">Email: ${settings.header.email}</div>` : ''}
            ${settings.header.website ? `<div class="contact">Website: ${settings.header.website}</div>` : ''}
            ${settings.header.gstNumber ? `<div class="contact">GST: ${settings.header.gstNumber}</div>` : ''}
            ${settings.header.customText ? `<div class="contact">${settings.header.customText}</div>` : ''}
          </div>

          <div class="divider"></div>

          <div class="order-info">
            <div>Order #: ${receipt.orderNumber}</div>
            <div>Date: ${receipt.date}</div>
            <div>Time: ${receipt.time}</div>
          </div>

          <div class="divider"></div>

          <div class="items">
            ${receipt.items.map((item: any) => `
              <div class="item">
                <span>${item.name} x${item.quantity}</span>
                <span>₹${item.total.toFixed(2)}</span>
              </div>
            `).join('')}
          </div>

          <div class="divider"></div>

          <div class="totals">
            <div class="total-row">
              <span>Subtotal:</span>
              <span>₹${receipt.subtotal.toFixed(2)}</span>
            </div>
            ${settings.format.showTaxBreakdown ? `
              <div class="total-row">
                <span>Tax (18%):</span>
                <span>₹${receipt.tax.toFixed(2)}</span>
              </div>
            ` : ''}
            <div class="total-row grand-total">
              <span>Total:</span>
              <span>₹${receipt.total.toFixed(2)}</span>
            </div>
          </div>

          <div class="footer">
            <div>${settings.footer.thankYouMessage}</div>
            ${settings.footer.termsAndConditions ? `<div style="margin-top: 10px; font-size: 10px;">${settings.footer.termsAndConditions}</div>` : ''}
            ${settings.footer.customText ? `<div style="margin-top: 10px;">${settings.footer.customText}</div>` : ''}
            ${settings.footer.showQRCode && settings.footer.qrCodeData ? `
              <div class="qr-code">
                <div>Scan QR Code:</div>
                <div style="margin-top: 5px; font-size: 10px;">${settings.footer.qrCodeData}</div>
              </div>
            ` : ''}
          </div>
        </div>
      </body>
      </html>
    `;
  };

  const updateSetting = (path: string, value: any) => {
    const pathArray = path.split('.');
    const newSettings = { ...settings };
    let current: any = newSettings;

    for (let i = 0; i < pathArray.length - 1; i++) {
      current = current[pathArray[i]];
    }
    current[pathArray[pathArray.length - 1]] = value;

    setSettings(newSettings);
  };

  if (isLoading) {
    return (
      <div className="billing-settings-tab">
        <div className="settings-loading">
          <div className="loading-spinner"></div>
          <p>Loading billing settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="billing-settings-tab">
      {/* Toast Notification */}
      {toast && (
        <div className={`modern-toast modern-toast-${toast.type}`}>
          <div className="toast-content">
            <span className="toast-icon">
              {toast.type === 'success' && '✅'}
              {toast.type === 'error' && '❌'}
              {toast.type === 'info' && 'ℹ️'}
            </span>
            <span className="toast-message">{toast.message}</span>
            <button className="toast-close" onClick={() => setToast(null)}>×</button>
          </div>
        </div>
      )}

      {/* Receipt Header Settings */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Receipt Header</h3>
        </div>
        
        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.header.showLogo}
              onChange={(e) => updateSetting('header.showLogo', e.target.checked)}
            />
            <div className="checkbox-content">
              <span className="checkbox-text">Show logo on receipts</span>
            </div>
          </label>
        </div>

        {settings.header.showLogo && (
          <div className="form-group">
            <ImageUpload
              currentImage={settings.header.logoUrl}
              onImageChange={(imageUrl) => updateSetting('header.logoUrl', imageUrl)}
              onImageRemove={() => updateSetting('header.logoUrl', '')}
              label="Receipt Logo"
              placeholder="Upload your restaurant logo"
            />
          </div>
        )}

        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Restaurant Name</label>
            <input
              type="text"
              className="form-input"
              value={settings.header.restaurantName}
              onChange={(e) => updateSetting('header.restaurantName', e.target.value)}
              placeholder="Enter restaurant name"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Phone Number</label>
            <input
              type="tel"
              className="form-input"
              value={settings.header.phone}
              onChange={(e) => updateSetting('header.phone', e.target.value)}
              placeholder="Enter phone number"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Email</label>
            <input
              type="email"
              className="form-input"
              value={settings.header.email}
              onChange={(e) => updateSetting('header.email', e.target.value)}
              placeholder="Enter email address"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Website</label>
            <input
              type="url"
              className="form-input"
              value={settings.header.website}
              onChange={(e) => updateSetting('header.website', e.target.value)}
              placeholder="Enter website URL"
            />
          </div>

          <div className="form-group">
            <label className="form-label">GST Number</label>
            <input
              type="text"
              className="form-input"
              value={settings.header.gstNumber}
              onChange={(e) => updateSetting('header.gstNumber', e.target.value)}
              placeholder="Enter GST number"
            />
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">Address</label>
          <textarea
            className="form-input"
            rows={3}
            value={settings.header.address}
            onChange={(e) => updateSetting('header.address', e.target.value)}
            placeholder="Enter complete address"
          />
        </div>

        <div className="form-group">
          <label className="form-label">Custom Header Text</label>
          <textarea
            className="form-input"
            rows={2}
            value={settings.header.customText}
            onChange={(e) => updateSetting('header.customText', e.target.value)}
            placeholder="Additional text to show in header (optional)"
          />
        </div>
      </div>

      {/* Receipt Footer Settings */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Receipt Footer</h3>
        </div>

        <div className="form-group">
          <label className="form-label">Thank You Message</label>
          <input
            type="text"
            className="form-input"
            value={settings.footer.thankYouMessage}
            onChange={(e) => updateSetting('footer.thankYouMessage', e.target.value)}
            placeholder="Thank you message"
          />
        </div>

        <div className="form-group">
          <label className="form-label">Terms and Conditions</label>
          <textarea
            className="form-input"
            rows={3}
            value={settings.footer.termsAndConditions}
            onChange={(e) => updateSetting('footer.termsAndConditions', e.target.value)}
            placeholder="Terms and conditions (optional)"
          />
        </div>

        <div className="form-group">
          <label className="form-label">Custom Footer Text</label>
          <textarea
            className="form-input"
            rows={2}
            value={settings.footer.customText}
            onChange={(e) => updateSetting('footer.customText', e.target.value)}
            placeholder="Additional footer text (optional)"
          />
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.footer.showQRCode}
              onChange={(e) => updateSetting('footer.showQRCode', e.target.checked)}
            />
            <span className="checkbox-text">Show QR Code on receipts</span>
          </label>
        </div>

        {settings.footer.showQRCode && (
          <div className="form-group">
            <label className="form-label">QR Code Data</label>
            <input
              type="text"
              className="form-input"
              value={settings.footer.qrCodeData}
              onChange={(e) => updateSetting('footer.qrCodeData', e.target.value)}
              placeholder="URL or text for QR code"
            />
          </div>
        )}
      </div>

      {/* Format Settings */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Receipt Format</h3>
        </div>

        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Paper Size</label>
            <select
              className="form-select"
              value={settings.format.paperSize}
              onChange={(e) => updateSetting('format.paperSize', e.target.value)}
            >
              <option value="thermal_80mm">Thermal 80mm</option>
              <option value="thermal_58mm">Thermal 58mm</option>
              <option value="a4">A4</option>
              <option value="letter">Letter</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Font Size</label>
            <select
              className="form-select"
              value={settings.format.fontSize}
              onChange={(e) => updateSetting('format.fontSize', e.target.value)}
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.format.showItemImages}
              onChange={(e) => updateSetting('format.showItemImages', e.target.checked)}
            />
            <span className="checkbox-text">Show item images on receipts</span>
          </label>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.format.showTaxBreakdown}
              onChange={(e) => updateSetting('format.showTaxBreakdown', e.target.checked)}
            />
            <span className="checkbox-text">Show detailed tax breakdown</span>
          </label>
        </div>
      </div>

      {/* Printer Settings */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Printer Settings</h3>
        </div>

        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Default Printer</label>
            <select
              className="form-select"
              value={settings.printer.printerName}
              onChange={(e) => updateSetting('printer.printerName', e.target.value)}
            >
              <option value="">Select Printer</option>
              {availablePrinters.map((printer, index) => (
                <option key={index} value={printer}>
                  {printer}
                </option>
              ))}
            </select>
            <small className="form-help">
              {availablePrinters.length === 0
                ? 'No printers detected. Please check your printer connections.'
                : `${availablePrinters.length} printer(s) detected`
              }
            </small>
          </div>

          <div className="form-group">
            <label className="form-label">Number of Copies</label>
            <input
              type="number"
              className="form-input"
              min="1"
              max="5"
              value={settings.printer.copies}
              onChange={(e) => updateSetting('printer.copies', parseInt(e.target.value))}
            />
          </div>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.printer.autoprint}
              onChange={(e) => updateSetting('printer.autoprint', e.target.checked)}
            />
            <div className="checkbox-content">
              <span className="checkbox-text">Auto-print receipts</span>
              <span className="checkbox-description">Automatically print receipts when orders are completed</span>
            </div>
          </label>
        </div>
      </div>

      {/* Save Button */}
      <div className="settings-section">
        <div className="section-actions">
          <button
            className="btn btn-primary"
            onClick={handleSave}
            disabled={isSaving}
          >
            {isSaving ? 'Saving...' : 'Save Billing Settings'}
          </button>
          <button
            className="btn btn-secondary"
            onClick={handlePreviewReceipt}
          >
            Preview Receipt
          </button>
        </div>
      </div>
    </div>
  );
};

export default BillingSettingsTab;
