<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zyka POS - Order Taking</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f8fafc;
            height: 100vh;
            overflow: hidden;
        }
        
        #pos-root {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .initial-loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            gap: 16px;
            background: #f8fafc;
        }

        .initial-loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .initial-loading-text {
            color: #64748b;
            font-size: 16px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div id="pos-root">
        <div class="initial-loading-container">
            <div class="initial-loading-spinner"></div>
            <div class="initial-loading-text">Loading Order Taking System...</div>
        </div>
    </div>

    <script>
        // Initialize POS window when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Order Taking Window loaded');
        });
    </script>
    <script src="pos-window.js"></script>
</body>
</html>
