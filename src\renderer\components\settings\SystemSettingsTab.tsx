import React, { useState, useEffect } from 'react';

interface SystemSettingsTabProps {
  restaurantId: string;
}

interface SystemSettings {
  currency: string;
  currencySymbol: string;
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  autoSaveOrders: boolean;
  soundEnabled: boolean;
  showItemImages: boolean;
  defaultTaxRate?: string;
}

interface ToastMessage {
  type: 'success' | 'error' | 'info';
  message: string;
}

const SystemSettingsTab: React.FC<SystemSettingsTabProps> = ({ restaurantId }) => {
  const [settings, setSettings] = useState<SystemSettings>({
    currency: 'INR',
    currencySymbol: '₹',
    language: 'en',
    timezone: 'Asia/Kolkata',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '12h',
    autoSaveOrders: true,
    soundEnabled: true,
    showItemImages: true,
    defaultTaxRate: undefined,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [toast, setToast] = useState<ToastMessage | null>(null);

  const currencies = [
    { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
    { code: 'USD', symbol: '$', name: 'US Dollar' },
    { code: 'EUR', symbol: '€', name: 'Euro' },
    { code: 'GBP', symbol: '£', name: 'British Pound' },
  ];

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'hi', name: 'Hindi' },
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
  ];

  const timezones = [
    'Asia/Kolkata',
    'America/New_York',
    'Europe/London',
    'Asia/Tokyo',
    'Australia/Sydney',
  ];

  const dateFormats = [
    'DD/MM/YYYY',
    'MM/DD/YYYY',
    'YYYY-MM-DD',
    'DD-MM-YYYY',
  ];

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Simulate API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
      setToast({ type: 'success', message: 'System settings saved successfully!' });
    } catch (error) {
      console.error('Error saving system settings:', error);
      setToast({ type: 'error', message: 'Failed to save system settings' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCreateBackup = async () => {
    setIsLoading(true);
    try {
      // Simulate backup creation
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create backup file name with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `zyka-backup-${timestamp}.json`;

      // Simulate backup data
      const backupData = {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        restaurantData: {
          // This would contain actual restaurant data
          name: 'Sample Restaurant',
          settings: settings
        },
        orders: [],
        menu: [],
        customers: []
      };

      // Create and download backup file
      const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = backupFileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      setToast({ type: 'success', message: 'Backup created and downloaded successfully!' });
    } catch (error) {
      console.error('Error creating backup:', error);
      setToast({ type: 'error', message: 'Failed to create backup' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewBackupHistory = () => {
    // Simulate backup history
    const backupHistory = [
      { date: '2024-01-15', size: '2.3 MB', status: 'Success' },
      { date: '2024-01-14', size: '2.1 MB', status: 'Success' },
      { date: '2024-01-13', size: '2.0 MB', status: 'Success' }
    ];

    const historyHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Backup History</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
          th { background-color: #f2f2f2; }
          .success { color: green; }
          .error { color: red; }
        </style>
      </head>
      <body>
        <h2>Backup History</h2>
        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Size</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            ${backupHistory.map(backup => `
              <tr>
                <td>${backup.date}</td>
                <td>${backup.size}</td>
                <td class="${backup.status.toLowerCase()}">${backup.status}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;

    const historyWindow = window.open('', '_blank', 'width=600,height=400,scrollbars=yes');
    if (historyWindow) {
      historyWindow.document.write(historyHTML);
      historyWindow.document.close();
    }
  };

  const updateSetting = (key: keyof SystemSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));

    // Auto-update currency symbol when currency changes
    if (key === 'currency') {
      const currency = currencies.find(c => c.code === value);
      if (currency) {
        setSettings(prev => ({
          ...prev,
          currencySymbol: currency.symbol
        }));
      }
    }
  };

  return (
    <div className="system-settings-tab">
      {/* Toast Notification */}
      {toast && (
        <div className={`modern-toast modern-toast-${toast.type}`}>
          <div className="toast-content">
            <span className="toast-icon">
              {toast.type === 'success' && '✅'}
              {toast.type === 'error' && '❌'}
              {toast.type === 'info' && 'ℹ️'}
            </span>
            <span className="toast-message">{toast.message}</span>
            <button className="toast-close" onClick={() => setToast(null)}>×</button>
          </div>
        </div>
      )}

      {/* Regional Settings */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Regional Settings</h3>
        </div>
        
        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Currency</label>
            <select
              className="form-select"
              value={settings.currency}
              onChange={(e) => updateSetting('currency', e.target.value)}
            >
              {currencies.map(currency => (
                <option key={currency.code} value={currency.code}>
                  {currency.symbol} {currency.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Language</label>
            <select
              className="form-select"
              value={settings.language}
              onChange={(e) => updateSetting('language', e.target.value)}
            >
              {languages.map(language => (
                <option key={language.code} value={language.code}>
                  {language.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Timezone</label>
            <select
              className="form-select"
              value={settings.timezone}
              onChange={(e) => updateSetting('timezone', e.target.value)}
            >
              {timezones.map(timezone => (
                <option key={timezone} value={timezone}>
                  {timezone}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Date Format</label>
            <select
              className="form-select"
              value={settings.dateFormat}
              onChange={(e) => updateSetting('dateFormat', e.target.value)}
            >
              {dateFormats.map(format => (
                <option key={format} value={format}>
                  {format}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Time Format</label>
            <select
              className="form-select"
              value={settings.timeFormat}
              onChange={(e) => updateSetting('timeFormat', e.target.value)}
            >
              <option value="12h">12 Hour (AM/PM)</option>
              <option value="24h">24 Hour</option>
            </select>
          </div>
        </div>
      </div>

      {/* Application Settings */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Application Settings</h3>
        </div>
        
        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.autoSaveOrders}
              onChange={(e) => updateSetting('autoSaveOrders', e.target.checked)}
            />
            <div className="checkbox-content">
              <span className="checkbox-text">Auto-save orders</span>
              <span className="checkbox-description">Automatically save orders as they are created</span>
            </div>
          </label>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.soundEnabled}
              onChange={(e) => updateSetting('soundEnabled', e.target.checked)}
            />
            <div className="checkbox-content">
              <span className="checkbox-text">Enable sound notifications</span>
              <span className="checkbox-description">Play sounds for order updates and notifications</span>
            </div>
          </label>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.showItemImages}
              onChange={(e) => updateSetting('showItemImages', e.target.checked)}
            />
            <div className="checkbox-content">
              <span className="checkbox-text">Show item images in POS</span>
              <span className="checkbox-description">Display menu item images in the point of sale interface</span>
            </div>
          </label>
        </div>
      </div>

      {/* Data & Backup Settings */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Data & Backup</h3>
        </div>
        
        <div className="backup-info">
          <div className="info-card">
            <h4>Automatic Backups</h4>
            <p>Your data is automatically backed up daily to ensure data safety.</p>
            <div className="backup-actions">
              <button
                className="btn btn-secondary"
                onClick={handleCreateBackup}
                disabled={isLoading}
              >
                {isLoading ? 'Creating Backup...' : 'Create Manual Backup'}
              </button>
              <button
                className="btn btn-secondary"
                onClick={handleViewBackupHistory}
              >
                View Backup History
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="settings-section">
        <div className="section-actions">
          <button
            className="btn btn-primary"
            onClick={handleSave}
            disabled={isSaving}
          >
            {isSaving ? 'Saving...' : 'Save System Settings'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SystemSettingsTab;
