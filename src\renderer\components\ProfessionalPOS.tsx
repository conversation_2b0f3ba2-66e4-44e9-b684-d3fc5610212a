import React, { useState, useEffect } from 'react';
import { MenuItem, Table, Order, OrderItem, TaxRate } from '../types';
import { useMenu } from '../contexts/MenuContext';
import { eventBus, EVENTS } from '../utils/eventBus';
import ImageResolver from './ImageResolver';
import Icon from './Icon';

interface ProfessionalPOSProps {
  restaurantId: string;
}

type OrderType = 'dine-in' | 'takeaway' | 'delivery';

const ProfessionalPOS: React.FC<ProfessionalPOSProps> = ({ restaurantId }) => {
  const { menuItems } = useMenu();
  const [tables, setTables] = useState<Table[]>([]);
  const [taxRates, setTaxRates] = useState<TaxRate[]>([]);
  const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const [orderType, setOrderType] = useState<OrderType>('dine-in');
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    address: ''
  });
  const [paxCount, setPaxCount] = useState<number>(1);
  const [discount, setDiscount] = useState({
    type: 'none' as 'none' | 'percentage' | 'amount',
    value: 0
  });
  const [showOrderConfirmation, setShowOrderConfirmation] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingItems, setIsLoadingItems] = useState(false);

  useEffect(() => {
    loadTables();
    loadTaxRates();
  }, [restaurantId]);

  const loadTables = async () => {
    try {
      const data = await window.electronAPI.getTables(restaurantId);
      setTables(data);
    } catch (error) {
      console.error('Failed to load tables:', error);
    }
  };

  const loadTaxRates = async () => {
    try {
      const data = await window.electronAPI.getTaxRates(restaurantId);
      setTaxRates(data.filter(tax => tax.isActive));
    } catch (error) {
      console.error('Failed to load tax rates:', error);
    }
  };

  const categories = ['all', ...new Set(menuItems.map(item => item.category))];

  const filteredMenuItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory && item.available;
  });

  // Pagination
  const totalPages = Math.ceil(filteredMenuItems.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedItems = filteredMenuItems.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedCategory]);

  // Add loading state for better UX with large datasets
  React.useEffect(() => {
    if (filteredMenuItems.length > 100) {
      setIsLoadingItems(true);
      const timer = setTimeout(() => {
        setIsLoadingItems(false);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [filteredMenuItems.length, currentPage]);

  const addToOrder = (menuItem: MenuItem) => {
    const existingItem = currentOrder.find(item => item.menuItemId === menuItem.id);
    
    if (existingItem) {
      setCurrentOrder(currentOrder.map(item =>
        item.menuItemId === menuItem.id
          ? { ...item, quantity: item.quantity + 1, subtotal: (item.quantity + 1) * item.menuItemPrice }
          : item
      ));
    } else {
      const newOrderItem: OrderItem = {
        id: Date.now().toString(),
        menuItemId: menuItem.id,
        menuItemName: menuItem.name,
        menuItemPrice: menuItem.price,
        quantity: 1,
        subtotal: menuItem.price,
        status: 'pending'
      };
      setCurrentOrder([...currentOrder, newOrderItem]);
    }
  };

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromOrder(itemId);
      return;
    }
    
    setCurrentOrder(currentOrder.map(item =>
      item.id === itemId
        ? { ...item, quantity: newQuantity, subtotal: newQuantity * item.menuItemPrice }
        : item
    ));
  };

  const removeFromOrder = (itemId: string) => {
    setCurrentOrder(currentOrder.filter(item => item.id !== itemId));
  };

  const calculateSubtotal = () => {
    return currentOrder.reduce((total, item) => total + item.subtotal, 0);
  };

  const calculateTaxAmount = () => {
    const subtotal = calculateSubtotal();
    const defaultTax = taxRates.find(tax => tax.isDefault);
    if (!defaultTax) return 0;
    
    return defaultTax.type === 'percentage' 
      ? (subtotal * defaultTax.rate) / 100
      : defaultTax.rate;
  };

  const calculateDiscountAmount = () => {
    const subtotal = calculateSubtotal();
    if (discount.type === 'percentage') {
      return (subtotal * discount.value) / 100;
    } else if (discount.type === 'amount') {
      return Math.min(discount.value, subtotal); // Don't allow discount greater than subtotal
    }
    return 0;
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const tax = calculateTaxAmount();
    const discountAmount = calculateDiscountAmount();
    return subtotal + tax - discountAmount;
  };

  const handleOrderTypeChange = (type: OrderType) => {
    setOrderType(type);
    if (type !== 'dine-in') {
      setSelectedTable(null);
    }
  };



  const proceedToOrderConfirmation = () => {
    if (currentOrder.length === 0) {
      alert('Please add items to the order');
      return;
    }

    if (orderType === 'dine-in' && !selectedTable) {
      alert('Please select a table for dine-in orders');
      return;
    }

    setShowOrderConfirmation(true);
  };

  const clearOrder = () => {
    setCurrentOrder([]);
    setSelectedTable(null);
    setCustomerInfo({ name: '', phone: '', address: '' });
    setPaxCount(1);
    setDiscount({ type: 'none', value: 0 });
    setShowOrderConfirmation(false);
  };

  const generateKOTContent = (order: any) => {
    const timestamp = new Date().toLocaleString('en-IN');
    let kotContent = `=== KITCHEN ORDER TICKET ===\n`;
    kotContent += `Order: ${order.orderNumber}\n`;
    kotContent += `Table: ${order.tableNumber || 'N/A'}\n`;
    kotContent += `Type: ${order.orderType.toUpperCase()}\n`;
    kotContent += `Time: ${timestamp}\n`;
    kotContent += `Pax: ${order.paxCount || 1}\n`;
    kotContent += `\n--- ITEMS ---\n`;

    order.items.forEach((item: any) => {
      kotContent += `${item.quantity}x ${item.name}\n`;
    });

    kotContent += `\n=== END KOT ===`;
    return kotContent;
  };

  const createOrder = async () => {
    try {
      const orderData = {
        restaurantId,
        tableId: selectedTable?.id,
        tableNumber: selectedTable?.tableNumber,
        orderType,
        status: 'pending' as const,
        items: currentOrder,
        subtotal: calculateSubtotal(),
        taxAmount: calculateTaxAmount(),
        discountAmount: calculateDiscountAmount(),
        totalAmount: calculateTotal(),
        paymentStatus: 'pending' as const,
        customerName: customerInfo.name || undefined,
        customerPhone: customerInfo.phone || undefined,
        customerEmail: orderType === 'delivery' ? customerInfo.address : undefined, // Using email field for address temporarily
        notes: orderType === 'dine-in' && selectedTable ? `Table ${selectedTable.tableNumber} - ${paxCount} pax` : undefined,
        kotPrinted: false,
        billPrinted: false
      };

      const result = await (window.electronAPI as any).createOrder(orderData);
      
      if (result.success) {
        // Update table status if dine-in
        if (selectedTable && orderType === 'dine-in') {
          await window.electronAPI.updateTable(selectedTable.id, { status: 'occupied' });
          await loadTables();
          eventBus.emit(EVENTS.TABLE_UPDATED);
        }

        // Automatically print KOT and update order status to preparing
        const kotContent = generateKOTContent(result.order);
        alert(`KOT Generated for Order ${result.order.orderNumber}\n\n${kotContent}`);

        // Update order status to preparing and mark KOT as printed
        await (window.electronAPI as any).updateOrder(result.order.id, {
          kotPrinted: true,
          status: 'preparing'
        });

        // Emit events for dashboard refresh
        eventBus.emit(EVENTS.ORDER_CREATED, result.order);
        eventBus.emit(EVENTS.DASHBOARD_REFRESH);

        alert(`Order ${result.order.orderNumber} created successfully! Redirecting to order history...`);
        clearOrder();

        // Navigate to order management (order history)
        eventBus.emit('NAVIGATE_TO_ORDERS');
      } else {
        alert('Failed to create order: ' + result.error);
      }
    } catch (error) {
      console.error('Error creating order:', error);
      alert('Failed to create order');
    }
  };



  if (showOrderConfirmation) {
    return (
      <div className="billing-modal">
        <div className="modal-overlay" onClick={() => setShowOrderConfirmation(false)}></div>
        <div className="modal-content billing-content">
          <div className="modal-header">
            <h2>Confirm Order</h2>
            <button className="close-btn" onClick={() => setShowOrderConfirmation(false)}>×</button>
          </div>

          <div className="billing-details">
            <div className="order-info">
              <div className="info-row">
                <span>Order Type:</span>
                <span className="order-type-badge">{orderType.toUpperCase()}</span>
              </div>
              {selectedTable && (
                <div className="info-row">
                  <span>Table:</span>
                  <span>Table {selectedTable.tableNumber}</span>
                </div>
              )}
            </div>

            <div className="customer-info">
              <h3>Customer Information</h3>

              {/* Customer Name and Phone for all types */}
              <div className="form-row">
                <input
                  type="text"
                  placeholder={orderType === 'delivery' ? "Customer Name (Required)" : "Customer Name (Optional)"}
                  value={customerInfo.name}
                  onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
                  className="form-input"
                />
                <input
                  type="tel"
                  placeholder={orderType === 'delivery' ? "Phone Number (Required)" : "Phone Number (Optional)"}
                  value={customerInfo.phone}
                  onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                  className="form-input"
                />
              </div>

              {/* Delivery Address - only for delivery */}
              {orderType === 'delivery' && (
                <div className="form-row">
                  <textarea
                    placeholder="Delivery Address (Required)"
                    value={customerInfo.address}
                    onChange={(e) => setCustomerInfo({...customerInfo, address: e.target.value})}
                    className="form-textarea"
                    rows={3}
                  />
                </div>
              )}

              {/* Table and Pax info for dine-in */}
              {selectedTable && orderType === 'dine-in' && (
                <div className="dine-in-info">
                  <div className="table-pax-info">
                    <span>Table {selectedTable.tableNumber} - {paxCount} guest{paxCount > 1 ? 's' : ''}</span>
                  </div>
                  <div className="pax-controls-modal">
                    <label>Number of Guests:</label>
                    <div className="pax-controls">
                      <button
                        type="button"
                        className="pax-btn"
                        onClick={() => setPaxCount(Math.max(1, paxCount - 1))}
                        disabled={paxCount <= 1}
                      >
                        -
                      </button>
                      <span className="pax-count">{paxCount}</span>
                      <button
                        type="button"
                        className="pax-btn"
                        onClick={() => setPaxCount(Math.min(selectedTable.capacity, paxCount + 1))}
                        disabled={paxCount >= selectedTable.capacity}
                      >
                        +
                      </button>
                    </div>
                    <p className="pax-info">Maximum {selectedTable.capacity} guests</p>
                  </div>
                </div>
              )}
            </div>

            <div className="order-items">
              <h3>Order Items</h3>
              {currentOrder.map(item => (
                <div key={item.id} className="order-item">
                  <div className="item-details">
                    <span className="item-name">{item.menuItemName}</span>
                    <span className="item-price">₹{item.menuItemPrice}</span>
                  </div>
                  <div className="item-quantity">
                    <span>Qty: {item.quantity}</span>
                    <span className="item-subtotal">₹{item.subtotal}</span>
                  </div>
                </div>
              ))}
            </div>

            <div className="discount-section">
              <h3>Discount</h3>
              <div className="discount-controls">
                <select
                  value={discount.type}
                  onChange={(e) => setDiscount({...discount, type: e.target.value as 'none' | 'percentage' | 'amount', value: 0})}
                  className="discount-type-select"
                >
                  <option value="none">No Discount</option>
                  <option value="percentage">Percentage (%)</option>
                  <option value="amount">Amount (₹)</option>
                </select>
                {discount.type !== 'none' && (
                  <input
                    type="number"
                    min="0"
                    max={discount.type === 'percentage' ? 100 : calculateSubtotal()}
                    step={discount.type === 'percentage' ? 1 : 0.01}
                    value={discount.value}
                    onChange={(e) => setDiscount({...discount, value: parseFloat(e.target.value) || 0})}
                    className="discount-value-input"
                    placeholder={discount.type === 'percentage' ? "Enter %" : "Enter amount"}
                  />
                )}
              </div>
            </div>

            <div className="billing-summary">
              <div className="summary-row">
                <span>Subtotal:</span>
                <span>₹{calculateSubtotal().toFixed(2)}</span>
              </div>
              <div className="summary-row">
                <span>
                  {(() => {
                    const defaultTax = taxRates.find(tax => tax.isDefault);
                    return defaultTax ? defaultTax.name : 'Tax';
                  })()}:
                </span>
                <span>₹{calculateTaxAmount().toFixed(2)}</span>
              </div>
              {discount.type !== 'none' && calculateDiscountAmount() > 0 && (
                <div className="summary-row discount">
                  <span>Discount ({discount.type === 'percentage' ? `${discount.value}%` : `₹${discount.value}`}):</span>
                  <span>-₹{calculateDiscountAmount().toFixed(2)}</span>
                </div>
              )}
              <div className="summary-row total">
                <span>Total:</span>
                <span>₹{calculateTotal().toFixed(2)}</span>
              </div>
            </div>

            <div className="billing-actions">
              <button className="btn btn-secondary" onClick={() => setShowOrderConfirmation(false)}>
                Cancel
              </button>
              <button
                className="btn btn-primary"
                onClick={createOrder}
                disabled={
                  orderType === 'delivery' &&
                  (!customerInfo.name.trim() || !customerInfo.phone.trim() || !customerInfo.address.trim())
                }
              >
                Punch KOT
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="professional-pos">
      {/* POS Header */}
      <div className="pos-header-section">
        <div className="pos-title-section">
          <h1 className="pos-main-title">Point of Sale</h1>
          <p className="pos-subtitle">Create and manage customer orders</p>
        </div>

        <div className="pos-controls">
          <div className="order-type-selector">
            {(['dine-in', 'takeaway', 'delivery'] as OrderType[]).map(type => (
              <button
                key={type}
                className={`order-type-btn ${orderType === type ? 'active' : ''}`}
                onClick={() => handleOrderTypeChange(type)}
              >
                <Icon name={type === 'dine-in' ? 'dine-in' : type === 'takeaway' ? 'takeaway' : 'delivery'} size="sm" />
                {type === 'dine-in' ? 'Dine-In' : type === 'takeaway' ? 'Takeaway' : 'Delivery'}
              </button>
            ))}
          </div>

          {selectedTable && (
            <div className="selected-table-info">
              <Icon name="table" size="sm" />
              <span>Table {selectedTable.tableNumber}</span>
            </div>
          )}
        </div>
      </div>

      <div className="pos-content">
        <div className="menu-section">
          <div className="menu-controls">
            <div className="search-section">
              <input
                type="text"
                placeholder="Search menu items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
              />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="category-select"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
            </div>

            <div className="view-controls">
              <div className="view-mode-toggle">
                <button
                  className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                  onClick={() => setViewMode('grid')}
                  title="Grid View"
                >
                  ⊞
                </button>
                <button
                  className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
                  onClick={() => setViewMode('list')}
                  title="List View"
                >
                  ☰
                </button>
              </div>

              <select
                value={itemsPerPage}
                onChange={(e) => setItemsPerPage(Number(e.target.value))}
                className="items-per-page-select"
              >
                <option value={20}>20 items</option>
                <option value={50}>50 items</option>
                <option value={100}>100 items</option>
              </select>
            </div>
          </div>

          <div className="menu-info">
            <span className="items-count">
              Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredMenuItems.length)} of {filteredMenuItems.length} items
            </span>
            {totalPages > 1 && (
              <div className="pagination">
                <button
                  className="page-btn"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  ‹ Prev
                </button>
                <span className="page-info">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  className="page-btn"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next ›
                </button>
              </div>
            )}
          </div>

          <div className={`menu-items-container ${viewMode}`}>
            {isLoadingItems ? (
              <div className="menu-loading">
                <div className="loading-spinner"></div>
                <p>Loading menu items...</p>
              </div>
            ) : (
              <>
                {paginatedItems.map(item => (
                  <div key={item.id} className={`menu-item-card ${viewMode}`} onClick={() => addToOrder(item)}>
                    <div className="item-image-container">
                      <ImageResolver
                        src={item.image}
                        alt={item.name}
                        className="item-image"
                      />
                      <button className="add-btn" onClick={(e) => {
                        e.stopPropagation();
                        addToOrder(item);
                      }}>+</button>
                    </div>

                    <div className="item-info">
                      <h4 className="item-name" title={item.name}>{item.name}</h4>
                      <div className="item-price">₹{item.price.toFixed(2)}</div>
                      {viewMode === 'grid' && item.description && (
                        <p className="item-description" title={item.description}>{item.description}</p>
                      )}
                      {viewMode === 'list' && (
                        <div className="item-details">
                          <span className="item-category">{item.category}</span>
                          {item.description && (
                            <p className="item-description" title={item.description}>{item.description}</p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {paginatedItems.length === 0 && !isLoadingItems && (
                  <div className="no-items">
                    <div className="no-items-icon">
                      <Icon name="search" size="xl" />
                    </div>
                    <h3>No items found</h3>
                    <p>Try adjusting your search or category filter</p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        <div className="order-section">
          <div className="order-header">
            <h3>Current Order</h3>
            <div className="order-type-selector">
              <select
                value={orderType}
                onChange={(e) => {
                  setOrderType(e.target.value as OrderType);
                  if (e.target.value !== 'dine-in') {
                    setSelectedTable(null);
                  }
                }}
                className="order-type-select"
              >
                <option value="dine-in">Dine In</option>
                <option value="takeaway">Takeaway</option>
                <option value="delivery">Delivery</option>
              </select>
            </div>
          </div>

          {orderType === 'dine-in' && (
            <div className="table-selection-section">
              <label className="table-selection-label">Select Table:</label>
              <select
                value={selectedTable?.id || ''}
                onChange={(e) => {
                  const tableId = e.target.value;
                  const table = tables.find(t => t.id === tableId);
                  setSelectedTable(table || null);
                  if (table) {
                    setPaxCount(1); // Reset pax count when selecting new table
                  }
                }}
                className="table-dropdown"
              >
                <option value="">Select a table</option>
                {tables.filter(table => table.status === 'available').map(table => (
                  <option key={table.id} value={table.id}>
                    Table {table.tableNumber} ({table.capacity} pax)
                    {table.location ? ` - ${table.location}` : ''}
                  </option>
                ))}
              </select>

              {tables.filter(table => table.status === 'available').length === 0 && (
                <div className="no-tables-available">
                  <p>No tables available</p>
                </div>
              )}

              {selectedTable && (
                <div className="selected-table-info">
                  <Icon name="check-circle" size="sm" />
                  <span>Table {selectedTable.tableNumber} selected ({selectedTable.capacity} pax)</span>
                </div>
              )}
            </div>
          )}

          <div className="order-items">
            {currentOrder.length === 0 ? (
              <div className="empty-order">
                <p>No items in order</p>
                <p>Select items from the menu to start</p>
              </div>
            ) : (
              currentOrder.map(item => (
                <div key={item.id} className="order-item">
                  <div className="item-details">
                    <span className="item-name">{item.menuItemName}</span>
                    <span className="item-price">₹{item.menuItemPrice}</span>
                  </div>
                  <div className="quantity-controls">
                    <button onClick={() => updateQuantity(item.id, item.quantity - 1)}>-</button>
                    <span className="quantity">{item.quantity}</span>
                    <button onClick={() => updateQuantity(item.id, item.quantity + 1)}>+</button>
                  </div>
                  <div className="item-subtotal">₹{item.subtotal}</div>
                  <button className="remove-btn" onClick={() => removeFromOrder(item.id)}>×</button>
                </div>
              ))
            )}
          </div>

          {currentOrder.length > 0 && (
            <div className="order-summary">
              <div className="summary-row">
                <span>Subtotal:</span>
                <span>₹{calculateSubtotal().toFixed(2)}</span>
              </div>
              <div className="summary-row">
                <span>Tax:</span>
                <span>₹{calculateTaxAmount().toFixed(2)}</span>
              </div>
              <div className="summary-row total">
                <span>Total:</span>
                <span>₹{calculateTotal().toFixed(2)}</span>
              </div>
            </div>
          )}

          <div className="order-actions">
            <button
              className="btn btn-secondary"
              onClick={clearOrder}
              disabled={currentOrder.length === 0}
            >
              Cancel
            </button>
            <button
              className="btn btn-primary"
              onClick={() => {
                if (orderType === 'dine-in' && !selectedTable) {
                  alert('Please select a table for dine-in orders');
                  return;
                }
                proceedToOrderConfirmation();
              }}
              disabled={currentOrder.length === 0}
            >
              <Icon name="chevron-right" size="sm" />
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfessionalPOS;
