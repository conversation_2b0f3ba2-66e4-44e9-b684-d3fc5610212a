const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';

  return [
    // Main process configuration
    {
      entry: './src/main.ts',
      target: 'electron-main',
      output: {
        path: path.resolve(__dirname, 'dist'),
        filename: 'main.js',
        clean: false
      },
      module: {
        rules: [
          {
            test: /\.tsx?$/,
            use: 'ts-loader',
            exclude: /node_modules/
          }
        ]
      },
      resolve: {
        extensions: ['.tsx', '.ts', '.js']
      },
      externals: {
        'sqlite3': 'commonjs sqlite3'
      },
      devtool: isProduction ? 'source-map' : 'eval-source-map',
      mode: argv.mode || 'development',
      node: {
        __dirname: false,
        __filename: false
      }
    },
    // Preload script configuration
    {
      entry: './src/preload.ts',
      target: 'electron-preload',
      output: {
        path: path.resolve(__dirname, 'dist'),
        filename: 'preload.js',
        clean: false
      },
      module: {
        rules: [
          {
            test: /\.tsx?$/,
            use: 'ts-loader',
            exclude: /node_modules/
          }
        ]
      },
      resolve: {
        extensions: ['.tsx', '.ts', '.js']
      },
      devtool: isProduction ? 'source-map' : 'eval-source-map',
      mode: argv.mode || 'development'
    },
    // Renderer process configuration
    {
      entry: './src/renderer/index.tsx',
      target: 'electron-renderer',
      output: {
        path: path.resolve(__dirname, 'dist'),
        filename: 'renderer.js',
        clean: false
      },
      module: {
        rules: [
          {
            test: /\.tsx?$/,
            use: 'ts-loader',
            exclude: /node_modules/
          },
          {
            test: /\.css$/,
            use: ['style-loader', 'css-loader']
          },
          {
            test: /\.(png|jpe?g|gif|svg)$/,
            type: 'asset/resource'
          }
        ]
      },
      resolve: {
        extensions: ['.tsx', '.ts', '.js', '.jsx']
      },
      plugins: [
        new HtmlWebpackPlugin({
          template: './src/renderer/index.html',
          filename: 'index.html'
        })
      ],
      devtool: isProduction ? 'source-map' : 'eval-source-map',
      mode: argv.mode || 'development'
    },
    // POS Window configuration
    {
      entry: {
        'pos-window': './src/renderer/pos-window.tsx'
      },
      target: 'electron-renderer',
      output: {
        path: path.resolve(__dirname, 'dist'),
        filename: '[name].js',
        clean: false
      },
      module: {
        rules: [
          {
            test: /\.tsx?$/,
            use: 'ts-loader',
            exclude: /node_modules/
          },
          {
            test: /\.css$/,
            use: ['style-loader', 'css-loader']
          },
          {
            test: /\.(png|jpe?g|gif|svg)$/,
            type: 'asset/resource'
          }
        ]
      },
      resolve: {
        extensions: ['.tsx', '.ts', '.js', '.jsx']
      },
      plugins: [
        new HtmlWebpackPlugin({
          template: './src/renderer/pos-window.html',
          filename: 'pos-window.html'
        })
      ],
      devtool: isProduction ? 'source-map' : 'eval-source-map',
      mode: argv.mode || 'development'
    }
  ];
};
