.backup-management {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.backup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.backup-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.backup-actions {
  display: flex;
  gap: 10px;
}

.create-backup-btn,
.export-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.create-backup-btn {
  background: #4CAF50;
  color: white;
}

.create-backup-btn:hover:not(:disabled) {
  background: #45a049;
  transform: translateY(-1px);
}

.export-btn {
  background: #2196F3;
  color: white;
}

.export-btn:hover:not(:disabled) {
  background: #1976D2;
  transform: translateY(-1px);
}

.create-backup-btn:disabled,
.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.message {
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-weight: 500;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.backup-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.backup-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;
}

.backup-list h3 {
  margin: 0;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
  font-size: 18px;
}

.no-backups {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.backup-table {
  width: 100%;
}

.backup-table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr 1.5fr;
  gap: 15px;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

.backup-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr 1.5fr;
  gap: 15px;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.backup-row:last-child {
  border-bottom: none;
}

.backup-row:hover {
  background: #f8f9fa;
}

.filename {
  font-weight: 500;
  color: #333;
}

.type-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.type-badge.manual {
  background: #e3f2fd;
  color: #1976d2;
}

.type-badge.automatic {
  background: #e8f5e8;
  color: #2e7d32;
}

.type-badge.scheduled {
  background: #fff3e0;
  color: #f57c00;
}

.col-actions {
  display: flex;
  gap: 8px;
}

.restore-btn,
.delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.restore-btn {
  background: #4CAF50;
  color: white;
}

.restore-btn:hover:not(:disabled) {
  background: #45a049;
}

.delete-btn {
  background: #f44336;
  color: white;
}

.delete-btn:hover:not(:disabled) {
  background: #d32f2f;
}

.restore-btn:disabled,
.delete-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.backup-info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backup-info h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.backup-info ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
  line-height: 1.6;
}

.backup-info li {
  margin-bottom: 8px;
}

.backup-info strong {
  color: #333;
}

@media (max-width: 768px) {
  .backup-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .backup-actions {
    justify-content: center;
  }

  .backup-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .backup-table-header,
  .backup-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .backup-table-header {
    display: none;
  }

  .backup-row {
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 10px;
  }

  .backup-row:hover {
    background: white;
  }

  .col-actions {
    justify-content: center;
    margin-top: 10px;
  }
}
