# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Application Configuration
APP_NAME=Zyka POS
FROM_EMAIL="Zyka POS" <<EMAIL>>

# Development/Production Environment
NODE_ENV=development

# Google Analytics 4 Configuration (Software Owner Only)
# These credentials are used to track all users' business metrics centrally
# Users will never see or configure these settings

# Your GA4 Measurement ID (starts with G-)
GA4_MEASUREMENT_ID= G-HXMQ3CGJMF

# Your GA4 Measurement Protocol API Secret
GA4_API_SECRET=wSh2r8AHTJ2U77phM1TNVA

# Optional: Analytics Configuration
# How often to sync data (cron expression) - Every 6 hours by default
ANALYTICS_SYNC_INTERVAL=0 */6 * * *

# Enable/disable analytics (true/false)
ANALYTICS_ENABLED=true
