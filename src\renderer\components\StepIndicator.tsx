import React from 'react';

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep, totalSteps }) => {
  return (
    <div className="step-indicator">
      {Array.from({ length: totalSteps }, (_, index) => (
        <div
          key={index}
          className={`step-dot ${
            index < currentStep ? 'completed' : index === currentStep ? 'active' : ''
          }`}
        />
      ))}
    </div>
  );
};

export default StepIndicator;
