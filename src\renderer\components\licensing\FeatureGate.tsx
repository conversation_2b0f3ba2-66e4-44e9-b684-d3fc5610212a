import React, { useState, useEffect } from 'react';
import { useLicensing } from '../../hooks/useLicensing';
import SubscriptionPlans from './SubscriptionPlans';
import PaymentForm from './PaymentForm';
import { SubscriptionPlan } from '../../types';
import './FeatureGate.css';

interface FeatureGateProps {
  featureId: string;
  userId: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
}

const FeatureGate: React.FC<FeatureGateProps> = ({
  featureId,
  userId,
  children,
  fallback,
  showUpgradePrompt = true
}) => {
  const { subscriptionStatus, checkFeatureAccess } = useLicensing(userId);
  const [accessResult, setAccessResult] = useState<{ hasAccess: boolean; reason: string } | null>(null);
  const [showPlans, setShowPlans] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAccess = async () => {
      setIsLoading(true);
      const result = await checkFeatureAccess(featureId);
      setAccessResult(result);
      setIsLoading(false);
    };

    checkAccess();
  }, [featureId, checkFeatureAccess]);

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setShowPaymentForm(true);
    setShowPlans(false);
  };

  const handlePaymentSuccess = async () => {
    setShowPaymentForm(false);
    setSelectedPlan(null);
    
    // Refresh access status
    const result = await checkFeatureAccess(featureId);
    setAccessResult(result);
    
    // Show success message
    alert('Payment successful! You now have access to this feature.');
  };

  const handlePaymentCancel = () => {
    setShowPaymentForm(false);
    setSelectedPlan(null);
  };

  const getFeatureInfo = (featureId: string) => {
    const features: Record<string, { name: string; description: string; icon: string; requiredPlan: string }> = {
      ordering: {
        name: 'Order Management',
        description: 'Create and manage customer orders',
        icon: '📋',
        requiredPlan: 'Basic'
      },
      kot: {
        name: 'Kitchen Order Tickets',
        description: 'Print KOT for kitchen staff',
        icon: '🍳',
        requiredPlan: 'Basic'
      },
      menu_management: {
        name: 'Advanced Menu Management',
        description: 'Full menu customization and categories',
        icon: '🍽️',
        requiredPlan: 'Premium'
      },
      table_management: {
        name: 'Table Management',
        description: 'Manage restaurant tables and reservations',
        icon: '🪑',
        requiredPlan: 'Premium'
      },
      analytics: {
        name: 'Analytics & Reports',
        description: 'Detailed sales reports and analytics',
        icon: '📊',
        requiredPlan: 'Premium'
      },
      inventory: {
        name: 'Inventory Management',
        description: 'Track stock and inventory levels',
        icon: '📦',
        requiredPlan: 'Premium'
      }
    };

    return features[featureId] || {
      name: 'Feature',
      description: 'This feature requires a subscription',
      icon: '🔒',
      requiredPlan: 'Premium'
    };
  };

  if (isLoading) {
    return (
      <div className="feature-gate-loading">
        <div className="loading-spinner"></div>
        <p>Checking access...</p>
      </div>
    );
  }

  if (accessResult?.hasAccess) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (!showUpgradePrompt) {
    return null;
  }

  const featureInfo = getFeatureInfo(featureId);

  return (
    <div className="feature-gate">
      <div className="feature-gate-content">
        <div className="feature-icon">{featureInfo.icon}</div>
        <h3 className="feature-title">{featureInfo.name}</h3>
        <p className="feature-description">{featureInfo.description}</p>
        
        <div className="access-info">
          <div className="required-plan">
            <span className="plan-badge">{featureInfo.requiredPlan} Plan Required</span>
          </div>
          
          {subscriptionStatus && (
            <div className="current-status">
              <p>
                Current Plan: <strong>
                  {subscriptionStatus.plan ? 
                    `${subscriptionStatus.plan.charAt(0).toUpperCase()}${subscriptionStatus.plan.slice(1)}` 
                    : 'None'
                  }
                </strong>
              </p>
              {subscriptionStatus.status === 'trial' && (
                <p className="trial-info">
                  Trial expires in {subscriptionStatus.daysRemaining} days
                </p>
              )}
            </div>
          )}
        </div>

        <div className="upgrade-actions">
          {subscriptionStatus?.status === 'trial' && subscriptionStatus?.daysRemaining > 0 ? (
            <div className="trial-message">
              <p>🎉 You're on a free trial! Upgrade now to continue using this feature after your trial expires.</p>
              <button 
                className="btn btn-primary"
                onClick={() => setShowPlans(true)}
              >
                Upgrade Now
              </button>
            </div>
          ) : subscriptionStatus?.status === 'expired' || (subscriptionStatus?.daysRemaining || 0) <= 0 ? (
            <div className="expired-message">
              <p>⚠️ Your subscription has expired. Renew now to access this feature.</p>
              <button 
                className="btn btn-primary"
                onClick={() => setShowPlans(true)}
              >
                Renew Subscription
              </button>
            </div>
          ) : (
            <div className="upgrade-message">
              <p>Upgrade to {featureInfo.requiredPlan} plan to access this feature.</p>
              <button 
                className="btn btn-primary"
                onClick={() => setShowPlans(true)}
              >
                View Plans
              </button>
            </div>
          )}
        </div>

        <div className="feature-benefits">
          <h4>What you'll get:</h4>
          <ul>
            {featureInfo.requiredPlan === 'Basic' ? (
              <>
                <li>Order Management</li>
                <li>Kitchen Order Tickets (KOT)</li>
                <li>Basic Billing</li>
                <li>Customer Management</li>
                <li>Basic Reports</li>
              </>
            ) : (
              <>
                <li>All Basic Plan features</li>
                <li>Advanced Menu Management</li>
                <li>Table Management & Reservations</li>
                <li>Inventory Management</li>
                <li>Advanced Analytics & Reports</li>
                <li>Multi-location Support</li>
                <li>Priority Support</li>
              </>
            )}
          </ul>
        </div>
      </div>

      {/* Modals */}
      {showPlans && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Choose Your Plan</h2>
              <button 
                className="close-btn"
                onClick={() => setShowPlans(false)}
              >
                ×
              </button>
            </div>
            <SubscriptionPlans
              userId={userId}
              currentStatus={subscriptionStatus || undefined}
              onPlanSelect={handlePlanSelect}
              showCurrentPlan={false}
            />
          </div>
        </div>
      )}

      {showPaymentForm && selectedPlan && (
        <PaymentForm
          plan={selectedPlan}
          userId={userId}
          onPaymentSuccess={handlePaymentSuccess}
          onCancel={handlePaymentCancel}
        />
      )}
    </div>
  );
};

export default FeatureGate;
