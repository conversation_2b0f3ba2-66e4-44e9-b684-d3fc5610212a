import React from 'react';
import Icon from './Icon';

interface KeyboardShortcutsHelpProps {
  onClose: () => void;
}

const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({ onClose }) => {
  const shortcuts = [
    { key: 'F', description: 'Focus search bar', category: 'Navigation' },
    { key: 'Ctrl + C', description: 'Clear current order', category: 'Order Management' },
    { key: 'Ctrl + D', description: 'Duplicate last item', category: 'Order Management' },
    { key: 'Ctrl + Enter', description: 'Create order', category: 'Order Management' },
    { key: '1', description: 'Switch to Dine-In', category: 'Order Type' },
    { key: '2', description: 'Switch to Takeaway', category: 'Order Type' },
    { key: '3', description: 'Switch to Delivery', category: 'Order Type' },
    { key: 'T', description: 'Open table selector (Dine-In)', category: 'Table Management' },
    { key: 'A', description: 'Show all menu items', category: 'Menu Navigation' },
    { key: 'Escape', description: 'Clear search / Close modals', category: 'Navigation' },
  ];

  const groupedShortcuts = shortcuts.reduce((groups, shortcut) => {
    if (!groups[shortcut.category]) {
      groups[shortcut.category] = [];
    }
    groups[shortcut.category].push(shortcut);
    return groups;
  }, {} as Record<string, typeof shortcuts>);

  return (
    <div className="keyboard-shortcuts-overlay">
      <div className="keyboard-shortcuts-modal">
        <div className="shortcuts-header">
          <h3>Keyboard Shortcuts</h3>
          <button className="close-btn" onClick={onClose}>
            <Icon name="cancel" size="sm" />
          </button>
        </div>

        <div className="shortcuts-content">
          <p className="shortcuts-intro">
            Use these keyboard shortcuts to work faster during busy periods:
          </p>

          <div className="shortcuts-grid">
            {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
              <div key={category} className="shortcuts-category">
                <h4 className="category-title">{category}</h4>
                <div className="shortcuts-list">
                  {categoryShortcuts.map((shortcut, index) => (
                    <div key={index} className="shortcut-item">
                      <div className="shortcut-key">
                        {shortcut.key.split(' + ').map((part, i, arr) => (
                          <React.Fragment key={i}>
                            <kbd>{part}</kbd>
                            {i < arr.length - 1 && <span className="plus">+</span>}
                          </React.Fragment>
                        ))}
                      </div>
                      <div className="shortcut-description">{shortcut.description}</div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="shortcuts-tip">
            <Icon name="info" size="sm" />
            <span>
              <strong>Pro Tip:</strong> Most shortcuts work when you're not typing in input fields.
              Press <kbd>?</kbd> anytime to show this help.
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KeyboardShortcutsHelp;
