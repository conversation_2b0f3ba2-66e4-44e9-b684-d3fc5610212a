import React, { useState, useEffect } from 'react';
import { SubscriptionPlan, SubscriptionStatus } from '../../types';
import './SubscriptionPlans.css';

interface SubscriptionPlansProps {
  userId: string;
  currentStatus?: SubscriptionStatus;
  onPlanSelect: (plan: SubscriptionPlan) => void;
  showCurrentPlan?: boolean;
}

const SubscriptionPlans: React.FC<SubscriptionPlansProps> = ({
  userId,
  currentStatus,
  onPlanSelect,
  showCurrentPlan = true
}) => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      setIsLoading(true);
      const availablePlans = await window.electronAPI.getAvailablePlans();
      setPlans(availablePlans);
    } catch (error) {
      console.error('Error loading plans:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(price);
  };

  const isCurrentPlan = (planId: string) => {
    return currentStatus?.plan === planId && currentStatus?.status === 'active';
  };

  const isPlanUpgrade = (planId: string) => {
    if (!currentStatus?.plan) return true;
    if (currentStatus.plan === 'basic' && planId === 'premium') return true;
    return false;
  };

  if (isLoading) {
    return (
      <div className="subscription-plans-loading">
        <div className="loading-spinner"></div>
        <p>Loading subscription plans...</p>
      </div>
    );
  }

  return (
    <div className="subscription-plans">
      <div className="plans-header">
        <h2>Choose Your Plan</h2>
        <p>Select the perfect plan for your restaurant's needs</p>
      </div>

      <div className="plans-grid">
        {plans.map((plan) => (
          <div
            key={plan.id}
            className={`plan-card ${plan.popular ? 'popular' : ''} ${
              isCurrentPlan(plan.id) ? 'current' : ''
            }`}
          >
            {plan.popular && (
              <div className="popular-badge">
                <span>Most Popular</span>
              </div>
            )}

            {isCurrentPlan(plan.id) && (
              <div className="current-badge">
                <span>Current Plan</span>
              </div>
            )}

            <div className="plan-header">
              <h3 className="plan-name">{plan.name}</h3>
              <div className="plan-price">
                <span className="price-amount">
                  {formatPrice(plan.price, plan.currency)}
                </span>
                <span className="price-period">/{plan.interval}</span>
              </div>
              <p className="plan-description">{plan.description}</p>
            </div>

            <div className="plan-features">
              <h4>Features included:</h4>
              <ul>
                {plan.features.map((feature, index) => (
                  <li key={index}>
                    <span className="feature-icon">✓</span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            <div className="plan-actions">
              {isCurrentPlan(plan.id) ? (
                <button className="btn btn-current" disabled>
                  Current Plan
                </button>
              ) : (
                <button
                  className={`btn ${plan.popular ? 'btn-primary' : 'btn-secondary'}`}
                  onClick={() => onPlanSelect(plan)}
                >
                  {isPlanUpgrade(plan.id) ? 'Upgrade' : 'Select'} Plan
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {showCurrentPlan && currentStatus && (
        <div className="current-subscription-info">
          <div className="subscription-status">
            <h3>Current Subscription Status</h3>
            <div className="status-details">
              <div className="status-item">
                <span className="label">Plan:</span>
                <span className="value">
                  {currentStatus.plan ? 
                    plans.find(p => p.id === currentStatus.plan)?.name || currentStatus.plan 
                    : 'No active plan'
                  }
                </span>
              </div>
              <div className="status-item">
                <span className="label">Status:</span>
                <span className={`value status-${currentStatus.status}`}>
                  {currentStatus.status === 'trial' ? 'Trial Period' : 
                   currentStatus.status === 'active' ? 'Active' :
                   currentStatus.status === 'expired' ? 'Expired' : 'Cancelled'}
                </span>
              </div>
              <div className="status-item">
                <span className="label">Days Remaining:</span>
                <span className={`value ${currentStatus.daysRemaining <= 3 ? 'warning' : ''}`}>
                  {currentStatus.daysRemaining} days
                </span>
              </div>
              {currentStatus.nextBillingDate && (
                <div className="status-item">
                  <span className="label">Next Billing:</span>
                  <span className="value">
                    {new Date(currentStatus.nextBillingDate).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionPlans;
