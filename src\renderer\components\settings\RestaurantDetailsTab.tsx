import React, { useState, useEffect } from 'react';
import { RestaurantDetails } from '../../types';
import { billingService } from '../../services/billingService';
import { settingsEventBus, SETTINGS_EVENTS } from '../../utils/settingsEventBus';

interface RestaurantDetailsTabProps {
  restaurantDetails: RestaurantDetails;
  onUpdateRestaurant?: (updates: Partial<RestaurantDetails>) => void;
}

interface FormErrors {
  [key: string]: string;
}

interface ToastMessage {
  type: 'success' | 'error' | 'info';
  message: string;
}

const RestaurantDetailsTab: React.FC<RestaurantDetailsTabProps> = ({
  restaurantDetails,
  onUpdateRestaurant
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [toast, setToast] = useState<ToastMessage | null>(null);

  const [restaurantForm, setRestaurantForm] = useState({
    restaurantName: restaurantDetails.restaurantName || '',
    restaurantType: restaurantDetails.restaurantType || 'Dine-In',
    location: restaurantDetails.location || '',
    restaurantAddress: restaurantDetails.restaurantAddress || '',
    phone: restaurantDetails.phone || '',
    email: restaurantDetails.email || '',
    website: restaurantDetails.website || '',
    description: restaurantDetails.description || '',
    gstNumber: restaurantDetails.gstNumber || ''
  });

  // Update form data when props change
  useEffect(() => {
    setRestaurantForm({
      restaurantName: restaurantDetails.restaurantName || '',
      restaurantType: restaurantDetails.restaurantType || 'Dine-In',
      location: restaurantDetails.location || '',
      restaurantAddress: restaurantDetails.restaurantAddress || '',
      phone: restaurantDetails.phone || '',
      email: restaurantDetails.email || '',
      website: restaurantDetails.website || '',
      description: restaurantDetails.description || '',
      gstNumber: restaurantDetails.gstNumber || ''
    });
  }, [restaurantDetails]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!restaurantForm.restaurantName.trim()) {
      newErrors.restaurantName = 'Restaurant name is required';
    }

    if (!restaurantForm.restaurantAddress.trim()) {
      newErrors.restaurantAddress = 'Restaurant address is required';
    }

    if (!restaurantForm.location.trim()) {
      newErrors.location = 'Location is required';
    }

    if (restaurantForm.email && !/\S+@\S+\.\S+/.test(restaurantForm.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (restaurantForm.phone && !/^\+?[\d\s\-\(\)]+$/.test(restaurantForm.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (restaurantForm.website && !/^https?:\/\/.+/.test(restaurantForm.website)) {
      newErrors.website = 'Please enter a valid website URL (starting with http:// or https://)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCancel = () => {
    setRestaurantForm({
      restaurantName: restaurantDetails.restaurantName || '',
      restaurantType: restaurantDetails.restaurantType || 'Dine-In',
      location: restaurantDetails.location || '',
      restaurantAddress: restaurantDetails.restaurantAddress || '',
      phone: restaurantDetails.phone || '',
      email: restaurantDetails.email || '',
      website: restaurantDetails.website || '',
      description: restaurantDetails.description || '',
      gstNumber: restaurantDetails.gstNumber || ''
    });
    setIsEditing(false);
    setErrors({});
  };

  const handleSave = async () => {
    if (!validateForm()) {
      setToast({ type: 'error', message: 'Please fix the errors before saving' });
      return;
    }

    setIsSaving(true);
    setErrors({});

    try {
      if (onUpdateRestaurant) {
        await onUpdateRestaurant(restaurantForm);

        // Update billing service with new restaurant details
        const updatedDetails = {
          ...restaurantDetails,
          ...restaurantForm
        };
        billingService.setRestaurantDetails(updatedDetails);

        // Emit event to synchronize with other settings tabs
        settingsEventBus.emit(SETTINGS_EVENTS.RESTAURANT_DETAILS_UPDATED, {
          restaurantName: restaurantForm.restaurantName,
          restaurantAddress: restaurantForm.restaurantAddress,
          phone: restaurantForm.phone,
          email: restaurantForm.email,
          website: restaurantForm.website,
          gstNumber: restaurantForm.gstNumber,
        });
      }

      setIsEditing(false);
      setToast({ type: 'success', message: 'Restaurant details updated successfully!' });
    } catch (error) {
      console.error('Error updating restaurant details:', error);
      setToast({ type: 'error', message: 'Failed to update restaurant details. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="restaurant-details-tab">
      {/* Toast Notification */}
      {toast && (
        <div className={`modern-toast modern-toast-${toast.type}`}>
          <div className="toast-content">
            <span className="toast-icon">
              {toast.type === 'success' && '✅'}
              {toast.type === 'error' && '❌'}
              {toast.type === 'info' && 'ℹ️'}
            </span>
            <span className="toast-message">{toast.message}</span>
            <button className="toast-close" onClick={() => setToast(null)}>×</button>
          </div>
        </div>
      )}

      {/* Restaurant Information */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Restaurant Information</h3>
          <div className="section-actions">
            {!isEditing ? (
              <button
                className="btn btn-primary"
                onClick={() => setIsEditing(true)}
                disabled={isSaving}
              >
                Edit Details
              </button>
            ) : (
              <div className="edit-actions">
                <button
                  className="btn btn-secondary"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  Cancel
                </button>
                <button
                  className="btn btn-primary"
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Restaurant Name *</label>
            <div className="form-display readonly">
              {restaurantDetails.restaurantName || 'Not set'}
              <span className="readonly-note">This field cannot be edited</span>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Restaurant Type *</label>
            <div className="form-display readonly">
              {restaurantDetails.restaurantType || 'Not set'}
              <span className="readonly-note">This field cannot be edited</span>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Location *</label>
            {isEditing ? (
              <div>
                <input
                  type="text"
                  className={`form-input ${errors.location ? 'error' : ''}`}
                  value={restaurantForm.location}
                  onChange={(e) => setRestaurantForm({...restaurantForm, location: e.target.value})}
                  placeholder="Enter location (city, area)"
                />
                {errors.location && <span className="error-message">{errors.location}</span>}
              </div>
            ) : (
              <div className="form-display">{restaurantDetails.location || 'Not set'}</div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">Phone Number</label>
            {isEditing ? (
              <div>
                <input
                  type="tel"
                  className={`form-input ${errors.phone ? 'error' : ''}`}
                  value={restaurantForm.phone}
                  onChange={(e) => setRestaurantForm({...restaurantForm, phone: e.target.value})}
                  placeholder="Enter restaurant phone number"
                />
                {errors.phone && <span className="error-message">{errors.phone}</span>}
              </div>
            ) : (
              <div className="form-display">{restaurantDetails.phone || 'Not set'}</div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">Email Address</label>
            {isEditing ? (
              <div>
                <input
                  type="email"
                  className={`form-input ${errors.email ? 'error' : ''}`}
                  value={restaurantForm.email}
                  onChange={(e) => setRestaurantForm({...restaurantForm, email: e.target.value})}
                  placeholder="Enter restaurant email"
                />
                {errors.email && <span className="error-message">{errors.email}</span>}
              </div>
            ) : (
              <div className="form-display">{restaurantDetails.email || 'Not set'}</div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">Website</label>
            {isEditing ? (
              <div>
                <input
                  type="url"
                  className={`form-input ${errors.website ? 'error' : ''}`}
                  value={restaurantForm.website}
                  onChange={(e) => setRestaurantForm({...restaurantForm, website: e.target.value})}
                  placeholder="https://example.com"
                />
                {errors.website && <span className="error-message">{errors.website}</span>}
              </div>
            ) : (
              <div className="form-display">
                {restaurantDetails.website ? (
                  <a href={restaurantDetails.website} target="_blank" rel="noopener noreferrer" className="website-link">
                    {restaurantDetails.website}
                  </a>
                ) : (
                  'Not set'
                )}
              </div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">GST Number</label>
            {isEditing ? (
              <input
                type="text"
                className="form-input"
                value={restaurantForm.gstNumber}
                onChange={(e) => setRestaurantForm({...restaurantForm, gstNumber: e.target.value})}
                placeholder="Enter GST number (optional)"
              />
            ) : (
              <div className="form-display">{restaurantDetails.gstNumber || 'Not set'}</div>
            )}
          </div>

          <div className="form-group full-width">
            <label className="form-label">Restaurant Address *</label>
            {isEditing ? (
              <div>
                <textarea
                  className={`form-input ${errors.restaurantAddress ? 'error' : ''}`}
                  rows={2}
                  value={restaurantForm.restaurantAddress}
                  onChange={(e) => setRestaurantForm({...restaurantForm, restaurantAddress: e.target.value})}
                  placeholder="Enter complete restaurant address"
                />
                {errors.restaurantAddress && <span className="error-message">{errors.restaurantAddress}</span>}
              </div>
            ) : (
              <div className="form-display">{restaurantDetails.restaurantAddress || 'Not set'}</div>
            )}
          </div>

          <div className="form-group full-width">
            <label className="form-label">Description</label>
            {isEditing ? (
              <textarea
                className="form-input"
                rows={3}
                value={restaurantForm.description}
                onChange={(e) => setRestaurantForm({...restaurantForm, description: e.target.value})}
                placeholder="Describe your restaurant, cuisine type, specialties, etc."
              />
            ) : (
              <div className="form-display">{restaurantDetails.description || 'Not set'}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RestaurantDetailsTab;
