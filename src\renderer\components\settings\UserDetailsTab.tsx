import React, { useState, useEffect } from 'react';
import { UserDetails } from '../../types';

interface UserDetailsTabProps {
  userDetails: UserDetails;
  onUpdateUser?: (updates: Partial<UserDetails>) => void;
}

interface FormErrors {
  [key: string]: string;
}

interface ToastMessage {
  type: 'success' | 'error' | 'info';
  message: string;
}

const UserDetailsTab: React.FC<UserDetailsTabProps> = ({
  userDetails,
  onUpdateUser
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [toast, setToast] = useState<ToastMessage | null>(null);

  const [userForm, setUserForm] = useState({
    name: userDetails.name || userDetails.fullName || '',
    email: userDetails.email || '',
    phone: userDetails.phone || '',
    address: userDetails.address || ''
  });

  // Update form data when props change
  useEffect(() => {
    setUserForm({
      name: userDetails.name || userDetails.fullName || '',
      email: userDetails.email || '',
      phone: userDetails.phone || '',
      address: userDetails.address || ''
    });
  }, [userDetails]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!userForm.name.trim()) {
      newErrors.name = 'Full name is required';
    }

    if (!userForm.email.trim()) {
      newErrors.email = 'Email address is required';
    } else if (!/\S+@\S+\.\S+/.test(userForm.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (userForm.phone && !/^\+?[\d\s\-\(\)]+$/.test(userForm.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCancel = () => {
    setUserForm({
      name: userDetails.name || userDetails.fullName || '',
      email: userDetails.email || '',
      phone: userDetails.phone || '',
      address: userDetails.address || ''
    });
    setIsEditing(false);
    setErrors({});
  };

  const handleSave = async () => {
    if (!validateForm()) {
      setToast({ type: 'error', message: 'Please fix the errors before saving' });
      return;
    }

    setIsSaving(true);
    setErrors({});

    try {
      if (onUpdateUser) {
        await onUpdateUser(userForm);
      }

      setIsEditing(false);
      setToast({ type: 'success', message: 'User details updated successfully!' });
    } catch (error) {
      console.error('Error updating user details:', error);
      setToast({ type: 'error', message: 'Failed to update user details. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not available';
    return new Date(dateString).toLocaleDateString();
  };

  const getSubscriptionStatusBadge = () => {
    const status = userDetails.subscriptionStatus || 'trial';
    const statusConfig = {
      trial: { label: 'Free Trial', className: 'status-trial' },
      active: { label: 'Active', className: 'status-active' },
      expired: { label: 'Expired', className: 'status-expired' },
      cancelled: { label: 'Cancelled', className: 'status-cancelled' }
    };

    const config = statusConfig[status] || statusConfig.trial;
    return <span className={`status-badge ${config.className}`}>{config.label}</span>;
  };

  return (
    <div className="user-details-tab">
      {/* Toast Notification */}
      {toast && (
        <div className={`modern-toast modern-toast-${toast.type}`}>
          <div className="toast-content">
            <span className="toast-icon">
              {toast.type === 'success' && '✅'}
              {toast.type === 'error' && '❌'}
              {toast.type === 'info' && 'ℹ️'}
            </span>
            <span className="toast-message">{toast.message}</span>
            <button className="toast-close" onClick={() => setToast(null)}>×</button>
          </div>
        </div>
      )}

      {/* User Profile Header */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Personal Information</h3>
          <div className="section-actions">
            {!isEditing ? (
              <button
                className="btn btn-primary"
                onClick={() => setIsEditing(true)}
                disabled={isSaving}
              >
                Edit Details
              </button>
            ) : (
              <div className="edit-actions">
                <button
                  className="btn btn-secondary"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  Cancel
                </button>
                <button
                  className="btn btn-primary"
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            )}
          </div>
        </div>



        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Full Name *</label>
            {isEditing ? (
              <div>
                <input
                  type="text"
                  className={`form-input ${errors.name ? 'error' : ''}`}
                  value={userForm.name}
                  onChange={(e) => setUserForm({...userForm, name: e.target.value})}
                  placeholder="Enter your full name"
                />
                {errors.name && <span className="error-message">{errors.name}</span>}
              </div>
            ) : (
              <div className="form-display">{userDetails.name || userDetails.fullName || 'Not set'}</div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">Email Address *</label>
            {isEditing ? (
              <div>
                <input
                  type="email"
                  className={`form-input ${errors.email ? 'error' : ''}`}
                  value={userForm.email}
                  onChange={(e) => setUserForm({...userForm, email: e.target.value})}
                  placeholder="Enter your email"
                />
                {errors.email && <span className="error-message">{errors.email}</span>}
              </div>
            ) : (
              <div className="form-display">{userDetails.email || 'Not set'}</div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">Phone Number</label>
            {isEditing ? (
              <div>
                <input
                  type="tel"
                  className={`form-input ${errors.phone ? 'error' : ''}`}
                  value={userForm.phone}
                  onChange={(e) => setUserForm({...userForm, phone: e.target.value})}
                  placeholder="Enter your phone number"
                />
                {errors.phone && <span className="error-message">{errors.phone}</span>}
              </div>
            ) : (
              <div className="form-display">{userDetails.phone || 'Not set'}</div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">Account Status</label>
            <div className="form-display">
              {getSubscriptionStatusBadge()}
            </div>
          </div>

          <div className="form-group full-width">
            <label className="form-label">Address</label>
            {isEditing ? (
              <textarea
                className="form-input"
                rows={2}
                value={userForm.address}
                onChange={(e) => setUserForm({...userForm, address: e.target.value})}
                placeholder="Enter your address"
              />
            ) : (
              <div className="form-display">{userDetails.address || 'Not set'}</div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">Account Created</label>
            <div className="form-display readonly">
              {userDetails.trialStartDate ? formatDate(userDetails.trialStartDate) : 'Not available'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetailsTab;
