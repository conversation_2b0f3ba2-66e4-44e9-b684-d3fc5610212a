import React from 'react';
import { useNotifications } from '../../contexts/NotificationContext';
import { ToastNotification } from '../../types';
import './ToastContainer.css';

interface ToastProps {
  toast: ToastNotification;
  onRemove: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ toast, onRemove }) => {
  const getToastIcon = (type: ToastNotification['type']) => {
    switch (type) {
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'error':
        return '❌';
      case 'info':
      default:
        return 'ℹ️';
    }
  };

  const handleClose = () => {
    onRemove(toast.id);
  };

  const handleActionClick = () => {
    if (toast.action?.onClick) {
      toast.action.onClick();
      onRemove(toast.id);
    }
  };

  return (
    <div className={`toast toast-${toast.type}`}>
      <div className="toast-content">
        <div className="toast-icon">
          {getToastIcon(toast.type)}
        </div>
        <div className="toast-message">
          <div className="toast-title">{toast.title}</div>
          <div className="toast-text">{toast.message}</div>
        </div>
        <div className="toast-actions">
          {toast.action && (
            <button 
              className="toast-action-btn"
              onClick={handleActionClick}
            >
              {toast.action.label}
            </button>
          )}
          <button 
            className="toast-close-btn"
            onClick={handleClose}
            aria-label="Close notification"
          >
            ×
          </button>
        </div>
      </div>
    </div>
  );
};

const ToastContainer: React.FC = () => {
  const { state, removeToast } = useNotifications();

  if (state.toasts.length === 0) {
    return null;
  }

  return (
    <div className="toast-container">
      {state.toasts.map((toast) => (
        <Toast
          key={toast.id}
          toast={toast}
          onRemove={removeToast}
        />
      ))}
    </div>
  );
};

export default ToastContainer;
