import React from 'react';
import { MenuItem } from '../types';
import ImageResolver from './ImageResolver';

interface MenuItemCardProps extends MenuItem {
  onAddToOrder: (item: MenuItem) => void;
}

const MenuItemCard: React.FC<MenuItemCardProps> = (props) => {
  const { onAddToOrder, ...menuItem } = props;
  const { id, name, price, description, category, image, available = true } = menuItem;

  const handleClick = () => {
    if (available) {
      onAddToOrder(menuItem);
    }
  };

  return (
    <div 
      className={`menu-item ${!available ? 'unavailable' : ''}`}
      onClick={handleClick}
    >
      {image && (
        <div className="menu-item-image">
          <ImageResolver src={image} alt={name} />
        </div>
      )}
      <div className="menu-item-content">
        <div className="menu-item-header">
          <h4 className="menu-item-name">{name}</h4>
          <span className="menu-item-price">₹{price.toFixed(2)}</span>
        </div>
        {description && (
          <p className="menu-item-description">{description}</p>
        )}
        {category && (
          <span className="menu-item-category">{category}</span>
        )}
        {!available && (
          <div className="unavailable-overlay">
            <span>Out of Stock</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default MenuItemCard;
