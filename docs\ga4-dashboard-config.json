{"dashboards": {"business_overview": {"name": "Zyka POS - Business Overview", "description": "Key business metrics and KPIs for restaurant management", "widgets": [{"type": "metric_card", "title": "Total Revenue (This Month)", "metric": "purchase", "aggregation": "sum", "dimension": "value", "filter": {"date_range": "this_month"}}, {"type": "metric_card", "title": "Active Subscriptions", "metric": "subscription_created", "aggregation": "count", "filter": {"status": "active"}}, {"type": "metric_card", "title": "New Users (This Week)", "metric": "user_registered", "aggregation": "count", "filter": {"date_range": "this_week"}}, {"type": "line_chart", "title": "Revenue Trend (Last 30 Days)", "metric": "purchase", "dimension": "date", "aggregation": "sum", "filter": {"date_range": "last_30_days"}}, {"type": "pie_chart", "title": "Revenue by Payment Method", "metric": "purchase", "dimension": "payment_method", "aggregation": "sum"}, {"type": "bar_chart", "title": "Subscription Plans Distribution", "metric": "subscription_created", "dimension": "plan_type", "aggregation": "count"}]}, "subscription_analytics": {"name": "Subscription Analytics", "description": "Detailed subscription and user lifecycle metrics", "widgets": [{"type": "funnel_chart", "title": "User Conversion Funnel", "steps": [{"name": "User Registration", "event": "user_registered"}, {"name": "Trial Started", "event": "trial_started"}, {"name": "Subscription Created", "event": "subscription_created"}, {"name": "First Payment", "event": "purchase"}]}, {"type": "cohort_table", "title": "User Retention by <PERSON><PERSON><PERSON>", "metric": "user_registered", "return_event": "subscription_updated", "period": "monthly"}, {"type": "line_chart", "title": "Trial to Paid Conversion Rate", "calculated_metric": {"numerator": "subscription_created", "denominator": "trial_started", "operation": "divide"}, "dimension": "date", "filter": {"date_range": "last_90_days"}}, {"type": "table", "title": "Subscription Status Breakdown", "dimensions": ["plan_type", "status"], "metrics": ["subscription_created", "subscription_updated"], "aggregation": "count"}]}, "restaurant_performance": {"name": "Restaurant Performance", "description": "Restaurant-specific metrics and operational insights", "widgets": [{"type": "geo_map", "title": "Restaurants by Location", "metric": "restaurant_created", "dimension": "location", "aggregation": "count"}, {"type": "bar_chart", "title": "Restaurants by Type", "metric": "restaurant_created", "dimension": "restaurant_type", "aggregation": "count"}, {"type": "table", "title": "Top Performing Restaurants", "dimensions": ["restaurant_name", "restaurant_type"], "metrics": ["purchase"], "aggregation": "sum", "sort": {"metric": "purchase", "order": "desc"}, "limit": 10}, {"type": "line_chart", "title": "New Restaurant Registrations", "metric": "restaurant_created", "dimension": "date", "aggregation": "count", "filter": {"date_range": "last_6_months"}}]}, "payment_analytics": {"name": "Payment Analytics", "description": "Payment processing and financial metrics", "widgets": [{"type": "metric_card", "title": "Payment Success Rate", "calculated_metric": {"numerator": {"metric": "payment_processed", "filter": {"status": "completed"}}, "denominator": "payment_processed", "operation": "divide", "format": "percentage"}}, {"type": "metric_card", "title": "Average Transaction Value", "metric": "purchase", "dimension": "value", "aggregation": "average"}, {"type": "pie_chart", "title": "Payment Methods Usage", "metric": "payment_processed", "dimension": "payment_method", "aggregation": "count"}, {"type": "line_chart", "title": "Failed Payments Trend", "metric": "payment_processed", "dimension": "date", "aggregation": "count", "filter": {"status": "failed", "date_range": "last_30_days"}}, {"type": "table", "title": "Payment Method Performance", "dimensions": ["payment_method"], "metrics": [{"name": "Total Transactions", "metric": "payment_processed", "aggregation": "count"}, {"name": "Success Rate", "calculated_metric": {"numerator": {"metric": "payment_processed", "filter": {"status": "completed"}}, "denominator": "payment_processed", "operation": "divide", "format": "percentage"}}, {"name": "Total Value", "metric": "purchase", "aggregation": "sum"}]}]}}, "custom_events": {"user_registered": {"description": "User creates account", "parameters": ["user_id", "subscription_status", "current_plan", "trial_start_date", "trial_end_date", "registration_date", "user_type"]}, "trial_started": {"description": "User begins trial period", "parameters": ["user_id", "trial_start_date", "trial_end_date", "plan_type"]}, "subscription_created": {"description": "New subscription activated", "parameters": ["user_id", "subscription_id", "plan_type", "status", "start_date", "end_date", "auto_renew", "payment_method"]}, "subscription_updated": {"description": "Subscription modified or renewed", "parameters": ["user_id", "subscription_id", "plan_type", "status", "auto_renew", "last_payment_date", "next_billing_date"]}, "restaurant_created": {"description": "New restaurant setup", "parameters": ["user_id", "restaurant_name", "restaurant_type", "location", "machine_code", "creation_date"]}, "payment_processed": {"description": "Payment attempt made", "parameters": ["user_id", "subscription_id", "transaction_id", "amount", "currency", "payment_method", "status", "gateway_transaction_id", "processing_date"]}, "purchase": {"description": "Successful payment (revenue event)", "parameters": ["user_id", "transaction_id", "value", "currency", "payment_method", "item_category"]}}, "custom_dimensions": {"user_plan_type": {"scope": "user", "description": "Current subscription plan of the user"}, "restaurant_type": {"scope": "event", "description": "Type of restaurant (Café, Takeaway, etc.)"}, "payment_method": {"scope": "event", "description": "Method used for payment"}, "subscription_status": {"scope": "user", "description": "Current subscription status"}}, "conversion_events": ["subscription_created", "trial_started", "purchase"], "audiences": {"trial_users": {"description": "Users currently in trial period", "conditions": [{"dimension": "subscription_status", "operator": "equals", "value": "trial"}]}, "premium_subscribers": {"description": "Users with premium subscriptions", "conditions": [{"dimension": "current_plan", "operator": "equals", "value": "premium"}]}, "at_risk_users": {"description": "Users with expiring subscriptions", "conditions": [{"dimension": "subscription_status", "operator": "equals", "value": "active"}, {"dimension": "days_until_expiry", "operator": "less_than", "value": 7}]}}, "alerts": [{"name": "Revenue Drop Alert", "condition": {"metric": "purchase", "aggregation": "sum", "period": "daily", "threshold": {"operator": "decreases_by", "value": 20, "unit": "percent"}}, "notification": {"email": true, "frequency": "immediate"}}, {"name": "High Payment Failure Rate", "condition": {"calculated_metric": {"numerator": {"metric": "payment_processed", "filter": {"status": "failed"}}, "denominator": "payment_processed", "operation": "divide"}, "threshold": {"operator": "greater_than", "value": 0.05}}, "notification": {"email": true, "frequency": "daily"}}]}