import React, { useState, useEffect } from 'react';
import { Order } from '../types';
import BillingSystem from './BillingSystem';
import PrintBillModal from './PrintBillModal';
import SettleBillModal from './SettleBillModal';
import { eventBus, EVENTS } from '../utils/eventBus';

interface OrderManagementProps {
  restaurantId: string;
}

const OrderManagement: React.FC<OrderManagementProps> = ({ restaurantId }) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showBilling, setShowBilling] = useState(false);
  const [billingOrder, setBillingOrder] = useState<Order | null>(null);
  const [showPrintBill, setShowPrintBill] = useState(false);
  const [printBillOrder, setPrintBillOrder] = useState<Order | null>(null);
  const [showSettleBill, setShowSettleBill] = useState(false);
  const [settleBillOrder, setSettleBillOrder] = useState<Order | null>(null);
  const [showDropdownMenu, setShowDropdownMenu] = useState<string | null>(null);

  const statusOptions = [
    { value: 'all', label: 'All Orders', color: '#6b7280' },
    { value: 'pending', label: 'Pending', color: '#f59e0b' },
    { value: 'confirmed', label: 'Confirmed', color: '#3b82f6' },
    { value: 'preparing', label: 'Preparing', color: '#8b5cf6' },
    { value: 'ready', label: 'Ready', color: '#10b981' },
    { value: 'served', label: 'Served', color: '#22c55e' },
    { value: 'completed', label: 'Completed', color: '#059669' },
    { value: 'cancelled', label: 'Cancelled', color: '#ef4444' }
  ];

  useEffect(() => {
    loadOrders();
  }, [restaurantId, selectedStatus]);

  // Listen for order updates and payment completion events
  useEffect(() => {
    const handleOrderUpdate = () => {
      loadOrders();
    };

    const handlePaymentCompleted = () => {
      loadOrders();
    };

    const handleDashboardRefresh = () => {
      loadOrders();
    };

    eventBus.on(EVENTS.ORDER_UPDATED, handleOrderUpdate);
    eventBus.on(EVENTS.PAYMENT_COMPLETED, handlePaymentCompleted);
    eventBus.on(EVENTS.DASHBOARD_REFRESH, handleDashboardRefresh);

    return () => {
      eventBus.off(EVENTS.ORDER_UPDATED, handleOrderUpdate);
      eventBus.off(EVENTS.PAYMENT_COMPLETED, handlePaymentCompleted);
      eventBus.off(EVENTS.DASHBOARD_REFRESH, handleDashboardRefresh);
    };
  }, []);

  const loadOrders = async () => {
    try {
      setIsLoading(true);
      const filters = selectedStatus !== 'all' ? { status: selectedStatus } : undefined;
      const data = await (window.electronAPI as any).getOrders(restaurantId, filters);
      setOrders(data);
    } catch (error) {
      console.error('Failed to load orders:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrderStatus = async (orderId: string, newStatus: Order['status']) => {
    try {
      const result = await (window.electronAPI as any).updateOrderStatus(orderId, newStatus);
      if (result.success) {
        await loadOrders();
        if (selectedOrder && selectedOrder.id === orderId) {
          setSelectedOrder(result.order);
        }

        // Emit events for dashboard refresh
        eventBus.emit(EVENTS.ORDER_UPDATED, result.order);
        eventBus.emit(EVENTS.DASHBOARD_REFRESH);
      } else {
        alert('Failed to update order status: ' + result.error);
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      alert('Failed to update order status');
    }
  };

  const printKOT = async (order: Order) => {
    try {
      // Update KOT printed status
      await (window.electronAPI as any).updateOrder(order.id, { kotPrinted: true });
      
      // Generate KOT content
      const kotContent = generateKOTContent(order);
      
      // For now, just show the KOT content in an alert
      // In a real implementation, this would send to a printer
      alert(`KOT Generated for Order ${order.orderNumber}\n\n${kotContent}`);
      
      await loadOrders();
    } catch (error) {
      console.error('Error printing KOT:', error);
      alert('Failed to print KOT');
    }
  };

  const generateKOTContent = (order: Order) => {
    const timestamp = new Date(order.createdAt).toLocaleString();
    let content = `=== KITCHEN ORDER TICKET ===\n`;
    content += `Order: ${order.orderNumber}\n`;
    content += `Time: ${timestamp}\n`;
    content += `Type: ${order.orderType.toUpperCase()}\n`;
    if (order.tableNumber) {
      content += `Table: ${order.tableNumber}\n`;
    }
    if (order.customerName) {
      content += `Customer: ${order.customerName}\n`;
    }
    content += `\n--- ITEMS ---\n`;
    
    order.items.forEach(item => {
      content += `${item.quantity}x ${item.menuItemName}\n`;
      if (item.notes) {
        content += `   Note: ${item.notes}\n`;
      }
    });
    
    if (order.notes) {
      content += `\nOrder Notes: ${order.notes}\n`;
    }
    
    content += `\n========================`;
    return content;
  };

  const getStatusInfo = (status: Order['status']) => {
    return statusOptions.find(option => option.value === status) || statusOptions[0];
  };

  const formatOrderType = (orderType: string) => {
    switch (orderType) {
      case 'dine-in':
        return 'Dine-In';
      case 'takeaway':
        return 'Takeaway';
      case 'delivery':
        return 'Delivery';
      default:
        return 'Dine-In';
    }
  };

  const getStatusActions = (order: Order) => {
    const actions = [];

    switch (order.status) {
      case 'pending':
        actions.push({ label: 'Confirm', status: 'confirmed' as const, color: '#3b82f6' });
        actions.push({ label: 'Cancel', status: 'cancelled' as const, color: '#ef4444' });
        break;
      case 'confirmed':
        actions.push({ label: 'Start Preparing', status: 'preparing' as const, color: '#8b5cf6' });
        break;
      case 'preparing':
        actions.push({ label: 'Mark Ready', status: 'ready' as const, color: '#10b981' });
        break;
      case 'ready':
        actions.push({ label: 'Mark Served', status: 'served' as const, color: '#22c55e' });
        break;
      case 'served':
        // Don't allow completion until payment is processed
        if (order.paymentStatus === 'paid') {
          actions.push({ label: 'Complete', status: 'completed' as const, color: '#059669' });
        }
        break;
    }

    return actions;
  };

  const handleBilling = (order: Order) => {
    setBillingOrder(order);
    setShowBilling(true);
  };

  const handlePaymentComplete = async (paymentData: any) => {
    try {
      await loadOrders();
      setShowBilling(false);
      setBillingOrder(null);

      // Events are already emitted by BillingSystem, just refresh local data
      eventBus.emit(EVENTS.DASHBOARD_REFRESH);
    } catch (error) {
      console.error('Error after payment completion:', error);
    }
  };

  const handlePrintBill = (order: Order) => {
    setPrintBillOrder(order);
    setShowPrintBill(true);
  };

  const handleSettleBill = (order: Order) => {
    setSettleBillOrder(order);
    setShowSettleBill(true);
  };

  const handleCancelOrder = async (orderId: string) => {
    if (confirm('Are you sure you want to cancel this order?')) {
      try {
        await updateOrderStatus(orderId, 'cancelled');
        setShowDropdownMenu(null);
      } catch (error) {
        console.error('Error cancelling order:', error);
      }
    }
  };

  const handleViewKOT = (order: Order) => {
    const kotContent = generateKOTContent(order);
    alert(`KOT for Order ${order.orderNumber}\n\n${kotContent}`);
    setShowDropdownMenu(null);
  };

  const handleOrderSettled = async () => {
    await loadOrders();
    setShowSettleBill(false);
    setSettleBillOrder(null);
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-IN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN');
  };

  if (isLoading) {
    return (
      <div className="order-management-loading">
        <div className="loading-spinner"></div>
        <p>Loading orders...</p>
      </div>
    );
  }

  return (
    <div className="order-management">
      <div className="order-management-header">
        <h2>Order Management</h2>
        <div className="status-filter">
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="status-select"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <button className="refresh-btn" onClick={loadOrders}>
            🔄 Refresh
          </button>
        </div>
      </div>

      <div className="orders-list-container">
        {orders.length === 0 ? (
          <div className="empty-orders">
            <span className="empty-icon">📋</span>
            <h3>No orders found</h3>
            <p>Orders will appear here when customers place them</p>
          </div>
        ) : (
          <div className="orders-table">
            <div className="orders-table-header">
              <div className="header-cell">Order No.</div>
              <div className="header-cell">Type</div>
              <div className="header-cell">Table No.</div>
              <div className="header-cell">Status</div>
              <div className="header-cell">Total</div>
              <div className="header-cell">Actions</div>
            </div>

            {orders.map(order => {
              const statusInfo = getStatusInfo(order.status);

              return (
                <div
                  key={order.id}
                  className={`order-row status-${order.status}`}
                  onClick={() => {
                    setSelectedOrder(order);
                    setShowOrderDetails(true);
                  }}
                  style={{ cursor: 'pointer' }}
                >
                  <div className="order-cell">{order.orderNumber}</div>
                  <div className="order-cell">
                    <span className="order-type-badge">
                      {formatOrderType(order.orderType)}
                    </span>
                  </div>
                  <div className="order-cell">{order.tableNumber || 'N/A'}</div>
                  <div className="order-cell">
                    <span className="status-badge" style={{ backgroundColor: statusInfo.color }}>
                      {statusInfo.label}
                    </span>
                  </div>
                  <div className="order-cell">₹{order.totalAmount.toFixed(2)}</div>
                  <div className="order-cell actions-cell" onClick={(e) => e.stopPropagation()}>
                    <button
                      className="btn btn-primary btn-sm"
                      onClick={() => handlePrintBill(order)}
                    >
                      Print Bill
                    </button>
                    <button
                      className="btn btn-success btn-sm"
                      onClick={() => handleSettleBill(order)}
                      disabled={order.paymentStatus === 'paid'}
                    >
                      Settle Bill
                    </button>
                    <div className="dropdown-container">
                      <button
                        className="btn btn-secondary btn-sm dropdown-toggle"
                        onClick={() => setShowDropdownMenu(showDropdownMenu === order.id ? null : order.id)}
                      >
                        ⋮
                      </button>
                      {showDropdownMenu === order.id && (
                        <div className="dropdown-menu">
                          <button
                            className="dropdown-item"
                            onClick={() => handleCancelOrder(order.id)}
                            disabled={order.status === 'completed' || order.status === 'cancelled'}
                          >
                            Cancel Order
                          </button>
                          <button
                            className="dropdown-item"
                            onClick={() => handleViewKOT(order)}
                          >
                            View KOT
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {showOrderDetails && selectedOrder && (
        <div className="order-details-modal">
          <div className="modal-overlay" onClick={() => setShowOrderDetails(false)}></div>
          <div className="modal-content">
            <div className="modal-header">
              <h2>Order Details - {selectedOrder.orderNumber}</h2>
              <button className="close-btn" onClick={() => setShowOrderDetails(false)}>×</button>
            </div>
            
            <div className="order-details-content">
              <div className="order-summary">
                <div className="summary-section">
                  <h3>Order Information</h3>
                  <div className="info-grid">
                    <div className="info-item">
                      <span className="label">Order Number:</span>
                      <span className="value">{selectedOrder.orderNumber}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">Status:</span>
                      <span className="value status-badge" style={{ backgroundColor: getStatusInfo(selectedOrder.status).color }}>
                        {getStatusInfo(selectedOrder.status).label}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="label">Type:</span>
                      <span className="value">{selectedOrder.orderType.toUpperCase()}</span>
                    </div>
                    {selectedOrder.tableNumber && (
                      <div className="info-item">
                        <span className="label">Table:</span>
                        <span className="value">Table {selectedOrder.tableNumber}</span>
                      </div>
                    )}
                    <div className="info-item">
                      <span className="label">Created:</span>
                      <span className="value">{formatDate(selectedOrder.createdAt)} at {formatTime(selectedOrder.createdAt)}</span>
                    </div>
                    {selectedOrder.customerName && (
                      <div className="info-item">
                        <span className="label">Customer:</span>
                        <span className="value">{selectedOrder.customerName}</span>
                      </div>
                    )}
                    {selectedOrder.customerPhone && (
                      <div className="info-item">
                        <span className="label">Phone:</span>
                        <span className="value">{selectedOrder.customerPhone}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="summary-section">
                  <h3>Order Items</h3>
                  <div className="items-list">
                    {selectedOrder.items.map(item => (
                      <div key={item.id} className="item-row">
                        <div className="item-info">
                          <span className="item-name">{item.menuItemName}</span>
                          <span className="item-price">₹{item.menuItemPrice}</span>
                        </div>
                        <div className="item-quantity">Qty: {item.quantity}</div>
                        <div className="item-subtotal">₹{item.subtotal.toFixed(2)}</div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="summary-section">
                  <h3>Payment Summary</h3>
                  <div className="payment-summary">
                    <div className="summary-row">
                      <span>Subtotal:</span>
                      <span>₹{selectedOrder.subtotal.toFixed(2)}</span>
                    </div>
                    <div className="summary-row">
                      <span>Tax:</span>
                      <span>₹{selectedOrder.taxAmount.toFixed(2)}</span>
                    </div>
                    <div className="summary-row total">
                      <span>Total:</span>
                      <span>₹{selectedOrder.totalAmount.toFixed(2)}</span>
                    </div>
                    <div className="summary-row">
                      <span>Payment Status:</span>
                      <span className={`payment-status ${selectedOrder.paymentStatus}`}>
                        {selectedOrder.paymentStatus.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {showBilling && billingOrder && (
        <BillingSystem
          order={billingOrder}
          onClose={() => {
            setShowBilling(false);
            setBillingOrder(null);
          }}
          onPaymentComplete={handlePaymentComplete}
        />
      )}

      {showPrintBill && printBillOrder && (
        <PrintBillModal
          order={printBillOrder}
          onClose={() => {
            setShowPrintBill(false);
            setPrintBillOrder(null);
          }}
        />
      )}

      {showSettleBill && settleBillOrder && (
        <SettleBillModal
          order={settleBillOrder}
          onClose={() => {
            setShowSettleBill(false);
            setSettleBillOrder(null);
          }}
          onSettled={handleOrderSettled}
        />
      )}
    </div>
  );
};

export default OrderManagement;
