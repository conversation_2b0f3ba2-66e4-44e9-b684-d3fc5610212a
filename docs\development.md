# Internal Development Guide

**⚠️ INTERNAL USE ONLY - Development Team**

This guide covers the development setup, workflow, and best practices for our internal development team working on Zyka POS.

## 🛠️ Prerequisites

### Required Software
- **Node.js** (v18.0.0 or higher)
- **npm** (v8.0.0 or higher) or **yarn** (v1.22.0 or higher)
- **Git** (v2.30.0 or higher)
- **Code Editor** (VS Code recommended)

### Recommended VS Code Extensions
- **TypeScript and JavaScript Language Features**
- **ES7+ React/Redux/React-Native snippets**
- **Prettier - Code formatter**
- **ESLint**
- **Auto Rename Tag**
- **Bracket Pair Colorizer**

## 🚀 Development Setup

### 1. Clone the Repository
```bash
git clone [internal-repository-url]
cd zyka-pos
```

### 2. Install Dependencies
```bash
npm install
# or
yarn install
```

### 3. Environment Configuration
Create a `.env` file in the root directory:
```env
# Database
DB_PATH=./data/zyka.db

# Development
NODE_ENV=development
DEBUG=true

# Analytics (Optional)
GA_MEASUREMENT_ID=your_ga_measurement_id
GA_API_SECRET=your_ga_api_secret

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

### 4. Start Development Server
```bash
# Start webpack in watch mode
npm run dev

# In another terminal, start Electron
npm run electron:dev
```

## 🏗️ Project Architecture

### Directory Structure
```
src/
├── main.ts                 # Electron main process
├── preload.ts             # Preload script for IPC
├── renderer/              # React frontend
│   ├── components/        # Reusable UI components
│   ├── pages/            # Main application pages
│   ├── contexts/         # React context providers
│   ├── hooks/            # Custom React hooks
│   ├── services/         # Frontend services
│   ├── styles/           # CSS styles
│   ├── types/            # TypeScript type definitions
│   └── utils/            # Utility functions
├── services/             # Backend services
│   ├── databaseService.ts # Database operations
│   ├── ipcHandlers.ts    # IPC communication handlers
│   ├── sqliteService.ts  # SQLite database service
│   ├── backupService.ts  # Backup and restore
│   └── analyticsService.ts # Analytics processing
└── types/                # Shared type definitions
```

### Technology Stack
- **Frontend**: React 19.1.0 + TypeScript 5.8.3
- **Backend**: Electron 37.2.3 + Node.js
- **Database**: SQLite3 5.1.7
- **Build Tool**: Webpack 5.100.2
- **Styling**: CSS3 with CSS Modules

## 🔧 Development Workflow

### 1. Feature Development
```bash
# Create a new branch
git checkout -b feature/your-feature-name

# Make your changes
# ... code changes ...

# Test your changes
npm run build
npm start

# Commit your changes
git add .
git commit -m "feat: add your feature description"

# Push and create PR
git push origin feature/your-feature-name
```

### 2. Bug Fixes
```bash
# Create a bug fix branch
git checkout -b fix/bug-description

# Fix the issue
# ... code changes ...

# Test the fix
npm run electron:dev

# Commit and push
git add .
git commit -m "fix: resolve bug description"
git push origin fix/bug-description
```

## 🧪 Testing

### Manual Testing
```bash
# Development mode
npm run electron:dev

# Production build testing
npm run build
npm start

# Test analytics functionality
npm run test:analytics
```

### Testing Checklist
- [ ] **Core POS functionality** works correctly
- [ ] **Table management** operations function properly
- [ ] **Menu management** CRUD operations work
- [ ] **Order processing** flows correctly
- [ ] **Analytics dashboard** displays accurate data
- [ ] **Settings** save and load properly
- [ ] **Database operations** complete successfully
- [ ] **UI responsiveness** on different screen sizes

## 🗄️ Database Development

### Database Schema
The application uses SQLite with the following main tables:
- `users` - User authentication and profiles
- `restaurants` - Restaurant information
- `tables` - Table management
- `menu_items` - Menu items and categories
- `orders` - Order processing
- `order_items` - Order line items
- `tax_rates` - Tax configuration
- `billing_settings` - Receipt configuration
- `app_settings` - Application preferences

### Database Migrations
```typescript
// Add new migration in sqliteService.ts
async runMigrations() {
  // Check for new table requirements
  const tableExists = await this.get("SELECT name FROM sqlite_master WHERE type='table' AND name='new_table'");
  if (!tableExists) {
    await this.run(`CREATE TABLE new_table (...)`);
  }
}
```

### Database Best Practices
- **Use transactions** for multiple related operations
- **Add proper indexes** for frequently queried columns
- **Use foreign key constraints** for data integrity
- **Handle errors gracefully** with try-catch blocks
- **Log database operations** for debugging

## 🎨 UI Development

### Component Structure
```tsx
import React, { useState, useEffect } from 'react';

interface ComponentProps {
  // Define props with proper types
  title: string;
  onAction?: () => void;
}

const Component: React.FC<ComponentProps> = ({ title, onAction }) => {
  // State management
  const [state, setState] = useState<StateType>(initialState);

  // Effects
  useEffect(() => {
    // Side effects
  }, [dependencies]);

  // Event handlers
  const handleAction = () => {
    // Handle user actions
    onAction?.();
  };

  // Render
  return (
    <div className="component">
      <h2>{title}</h2>
      <button onClick={handleAction}>Action</button>
    </div>
  );
};

export default Component;
```

### Styling Guidelines
- **Use CSS modules** for component-specific styles
- **Follow BEM methodology** for class naming
- **Use CSS variables** for consistent theming
- **Implement responsive design** with mobile-first approach
- **Optimize for touch interfaces** with appropriate sizing

## 🔌 IPC Communication

### Adding New IPC Handlers
1. **Define handler in `ipcHandlers.ts`**:
```typescript
ipcMain.handle('new-operation', async (_, param1, param2) => {
  try {
    // Perform operation
    const result = await performOperation(param1, param2);
    return { success: true, data: result };
  } catch (error) {
    console.error('Operation failed:', error);
    return { success: false, error: error.message };
  }
});
```

2. **Add to preload script**:
```typescript
// In preload.ts
newOperation: (param1: string, param2: number) => 
  ipcRenderer.invoke('new-operation', param1, param2),
```

3. **Add TypeScript types**:
```typescript
// In types/index.ts
newOperation: (param1: string, param2: number) => Promise<OperationResult>;
```

4. **Use in renderer**:
```typescript
const result = await window.electronAPI.newOperation('value', 123);
if (result.success) {
  // Handle success
} else {
  // Handle error
}
```

## 🚀 Build and Distribution

### Development Build
```bash
npm run build:dev
```

### Production Build
```bash
npm run build
```

### Create Distribution
```bash
# Package without installer
npm run pack

# Create installer
npm run dist
```

### Build Configuration
The build is configured in `webpack.config.js` and `package.json`:
- **Main process**: TypeScript compilation for Electron main
- **Renderer process**: React + TypeScript with Webpack
- **Preload script**: TypeScript compilation for IPC bridge

## 🐛 Debugging

### Electron Main Process
```bash
# Enable debug mode
DEBUG=* npm run electron:dev
```

### Renderer Process
- **Open DevTools**: `Ctrl+Shift+I` (Windows/Linux) or `Cmd+Opt+I` (Mac)
- **Console logging**: Use `console.log()`, `console.error()`, etc.
- **React DevTools**: Install browser extension for React debugging

### Database Debugging
```typescript
// Enable SQL logging
const db = new Database(dbPath, { verbose: console.log });
```

## 📝 Code Style

### TypeScript Guidelines
- **Use strict mode** with proper type definitions
- **Avoid `any` type** - use specific types or `unknown`
- **Use interfaces** for object shapes
- **Use enums** for constants with multiple values
- **Use proper naming conventions**

### React Guidelines
- **Use functional components** with hooks
- **Implement proper error boundaries**
- **Use React.memo** for performance optimization
- **Handle loading and error states**
- **Use proper key props** in lists

### General Guidelines
- **Write self-documenting code** with clear variable names
- **Add comments** for complex logic
- **Keep functions small** and focused
- **Use consistent formatting** (Prettier recommended)
- **Handle errors gracefully** with proper error messages

## 🔄 Hot Reload

The development setup includes hot reload for faster development:
- **Webpack watch mode** automatically rebuilds on file changes
- **Electron restart** when main process files change
- **React hot reload** for component changes without losing state

## 📊 Performance Optimization

### Frontend Performance
- **Use React.memo** for expensive components
- **Implement virtualization** for large lists
- **Optimize images** and assets
- **Use code splitting** for large bundles
- **Minimize re-renders** with proper dependency arrays

### Backend Performance
- **Use database indexes** for frequently queried columns
- **Implement connection pooling** for database operations
- **Cache frequently accessed data**
- **Optimize SQL queries** with proper joins and conditions
- **Use transactions** for bulk operations

## 🆘 Common Issues

### Build Issues
- **Clear node_modules**: `rm -rf node_modules && npm install`
- **Clear build cache**: `rm -rf dist && npm run build`
- **Check Node.js version**: Ensure v18+ is installed

### Database Issues
- **Database locked**: Ensure no other processes are using the database
- **Migration errors**: Check database schema and foreign key constraints
- **Permission errors**: Ensure write permissions for database directory

### Electron Issues
- **App won't start**: Check main process console for errors
- **IPC not working**: Verify preload script is loaded correctly
- **DevTools not opening**: Check if development mode is enabled

For more help, check the [Troubleshooting Guide](troubleshooting.md) or create an issue on GitHub.

## 🔗 Useful Resources

### Documentation
- [Electron Documentation](https://www.electronjs.org/docs)
- [React Documentation](https://reactjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [SQLite Documentation](https://www.sqlite.org/docs.html)

### Tools
- [Electron Forge](https://www.electronforge.io/) - Application packaging
- [React Developer Tools](https://react.dev/learn/react-developer-tools)
- [SQLite Browser](https://sqlitebrowser.org/) - Database inspection

### Community
- [Electron Discord](https://discord.gg/electron)
- [React Community](https://reactjs.org/community/support.html)
- [TypeScript Community](https://www.typescriptlang.org/community)

---

**Happy coding!** 🚀

If you have questions or need help, don't hesitate to reach out to the development team or create an issue on GitHub.
