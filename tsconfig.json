{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "allowSyntheticDefaultImports": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "typeRoots": ["./node_modules/@types", "./src/types"]}