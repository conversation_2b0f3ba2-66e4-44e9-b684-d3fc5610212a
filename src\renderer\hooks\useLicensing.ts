import { useState, useEffect, useCallback } from 'react';
import { SubscriptionStatus } from '../types';

interface LicensingHook {
  subscriptionStatus: SubscriptionStatus | null;
  isLoading: boolean;
  hasFeatureAccess: (featureId: string) => boolean;
  checkFeatureAccess: (featureId: string) => Promise<{ hasAccess: boolean; reason: string }>;
  refreshStatus: () => Promise<void>;
  isTrialExpired: boolean;
  isSubscriptionExpired: boolean;
  daysUntilExpiry: number;
}

export const useLicensing = (userId: string): LicensingHook => {
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [featureCache, setFeatureCache] = useState<Record<string, { hasAccess: boolean; reason: string }>>({});

  const refreshStatus = useCallback(async () => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      const status = await window.electronAPI.getSubscriptionStatus(userId);
      setSubscriptionStatus(status);
      
      // Clear feature cache when status changes
      setFeatureCache({});
    } catch (error) {
      console.error('Error refreshing subscription status:', error);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    refreshStatus();
  }, [refreshStatus]);

  const checkFeatureAccess = useCallback(async (featureId: string) => {
    if (!userId) {
      return { hasAccess: false, reason: 'No user ID provided' };
    }

    // Check cache first
    if (featureCache[featureId]) {
      return featureCache[featureId];
    }

    try {
      const result = await window.electronAPI.checkFeatureAccess(userId, featureId);
      
      // Cache the result
      setFeatureCache(prev => ({
        ...prev,
        [featureId]: result
      }));
      
      return result;
    } catch (error) {
      console.error('Error checking feature access:', error);
      return { hasAccess: false, reason: 'Error checking access' };
    }
  }, [userId, featureCache]);

  const hasFeatureAccess = useCallback((featureId: string): boolean => {
    if (!subscriptionStatus) return false;
    
    // Basic features available to all plans
    const basicFeatures = ['ordering', 'kot'];
    if (basicFeatures.includes(featureId)) {
      return subscriptionStatus.status === 'trial' || subscriptionStatus.status === 'active';
    }
    
    // Premium features
    const premiumFeatures = ['menu_management', 'table_management', 'analytics', 'inventory'];
    if (premiumFeatures.includes(featureId)) {
      return subscriptionStatus.status === 'active' && subscriptionStatus.plan === 'premium';
    }
    
    return false;
  }, [subscriptionStatus]);

  const isTrialExpired = subscriptionStatus?.status === 'trial' && subscriptionStatus?.daysRemaining <= 0;
  const isSubscriptionExpired = subscriptionStatus?.status === 'expired';
  const daysUntilExpiry = subscriptionStatus?.daysRemaining || 0;

  return {
    subscriptionStatus,
    isLoading,
    hasFeatureAccess,
    checkFeatureAccess,
    refreshStatus,
    isTrialExpired,
    isSubscriptionExpired,
    daysUntilExpiry
  };
};

export default useLicensing;
