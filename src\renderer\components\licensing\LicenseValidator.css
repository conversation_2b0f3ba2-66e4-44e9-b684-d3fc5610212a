.license-validator {
  position: relative;
  width: 100%;
  height: 100%;
}

.expired-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.expired-modal {
  background: white;
  border-radius: 16px;
  padding: 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.expired-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.expired-modal h2 {
  font-size: 28px;
  font-weight: 700;
  color: #dc2626;
  margin: 0 0 16px 0;
}

.expired-modal p {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.expired-actions {
  margin-bottom: 24px;
}

.limited-access-note {
  background: #fef3c7;
  border: 1px solid #fcd34d;
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
}

.limited-access-note p {
  font-size: 14px;
  color: #92400e;
  margin: 0;
  line-height: 1.5;
}

.limited-access-overlay {
  filter: grayscale(50%);
  opacity: 0.7;
  pointer-events: none;
  user-select: none;
}

.expiry-warning-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  z-index: 1500;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.warning-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.warning-icon {
  font-size: 20px;
  margin-right: 12px;
}

.warning-text {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-right: 20px;
}

.warning-text strong {
  font-weight: 600;
  margin-bottom: 2px;
}

.warning-text span {
  font-size: 14px;
  opacity: 0.9;
}

.warning-actions {
  display: flex;
  gap: 8px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-ghost:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-header h2 {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

@media (max-width: 768px) {
  .expired-modal {
    padding: 24px;
    margin: 20px;
  }
  
  .expired-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .expired-modal h2 {
    font-size: 24px;
  }
  
  .expired-modal p {
    font-size: 14px;
  }
  
  .warning-content {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .warning-text {
    margin-right: 0;
    text-align: center;
  }
  
  .warning-actions {
    justify-content: center;
  }
}
