// Settings Event Bus for synchronizing data between different settings tabs

export interface SettingsEventData {
  restaurantName?: string;
  restaurantAddress?: string;
  phone?: string;
  email?: string;
  website?: string;
  gstNumber?: string;
}

export const SETTINGS_EVENTS = {
  RESTAURANT_DETAILS_UPDATED: 'restaurant-details-updated',
  BILLING_SETTINGS_UPDATED: 'billing-settings-updated',
} as const;

class SettingsEventBus {
  private listeners: { [key: string]: Array<(data: any) => void> } = {};

  on(event: string, callback: (data: any) => void) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  off(event: string, callback: (data: any) => void) {
    if (!this.listeners[event]) return;
    
    const index = this.listeners[event].indexOf(callback);
    if (index > -1) {
      this.listeners[event].splice(index, 1);
    }
  }

  emit(event: string, data?: any) {
    if (!this.listeners[event]) return;
    
    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in settings event listener:', error);
      }
    });
  }

  // Remove all listeners for cleanup
  removeAllListeners(event?: string) {
    if (event) {
      delete this.listeners[event];
    } else {
      this.listeners = {};
    }
  }
}

export const settingsEventBus = new SettingsEventBus();
