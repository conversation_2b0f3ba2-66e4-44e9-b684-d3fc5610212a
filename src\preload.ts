import { contextBridge, ipc<PERSON>enderer } from 'electron';

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  generateUserCredentials: () => ipcRenderer.invoke('generate-user-credentials'),
  sendEmail: (data: { email: string; userId: string; pin: string }) =>
    ipcRenderer.invoke('send-email', data),
  generateMachineCode: () => ipcRenderer.invoke('generate-machine-code'),
  storeUserData: (data: any) => ipcRenderer.invoke('store-user-data', data),
  getUserData: () => ipcRenderer.invoke('get-user-data'),
  validatePin: (data: { pin: string }) => ipcRenderer.invoke('validate-pin', data),
  storeRestaurantData: (data: any) => ipcRenderer.invoke('store-restaurant-data', data),
  checkSetupStatus: () => ipc<PERSON>enderer.invoke('check-setup-status'),
  getRestaurantData: () => ipc<PERSON>enderer.invoke('get-restaurant-data'),
  getRestaurantDetails: (restaurantId: string) => ipcRenderer.invoke('get-restaurant-details', restaurantId),
  getMenuItems: (restaurantId?: string) => ipcRenderer.invoke('get-menu-items', restaurantId),
  createMenuItem: (menuItem: any) => ipcRenderer.invoke('create-menu-item', menuItem),
  updateMenuItem: (id: string, updates: any) => ipcRenderer.invoke('update-menu-item', id, updates),
  deleteMenuItem: (id: string) => ipcRenderer.invoke('delete-menu-item', id),
  bulkImportMenuItems: (menuItems: any[], restaurantId: string) => ipcRenderer.invoke('bulk-import-menu-items', menuItems, restaurantId),
  saveImage: (imageData: string, fileName: string) => ipcRenderer.invoke('save-image', imageData, fileName),
  getImagePath: (relativePath: string) => ipcRenderer.invoke('get-image-path', relativePath),
  deleteImage: (relativePath: string) => ipcRenderer.invoke('delete-image', relativePath),
  // Table Management
  getTables: (restaurantId?: string) => ipcRenderer.invoke('get-tables', restaurantId),
  createTable: (table: any) => ipcRenderer.invoke('create-table', table),
  updateTable: (id: string, updates: any) => ipcRenderer.invoke('update-table', id, updates),
  deleteTable: (id: string) => ipcRenderer.invoke('delete-table', id),
  // Tax Management
  getTaxRates: (restaurantId?: string) => ipcRenderer.invoke('get-tax-rates', restaurantId),
  createTaxRate: (taxRate: any) => ipcRenderer.invoke('create-tax-rate', taxRate),
  updateTaxRate: (id: string, updates: any) => ipcRenderer.invoke('update-tax-rate', id, updates),
  deleteTaxRate: (id: string) => ipcRenderer.invoke('delete-tax-rate', id),
  // Settings Management
  getBillingSettings: (restaurantId: string) => ipcRenderer.invoke('get-billing-settings', restaurantId),
  saveBillingSettings: (settings: any) => ipcRenderer.invoke('save-billing-settings', settings),
  getAppSettings: (restaurantId: string) => ipcRenderer.invoke('get-app-settings', restaurantId),
  saveAppSettings: (settings: any) => ipcRenderer.invoke('save-app-settings', settings),
  // Analytics
  getAnalyticsData: (restaurantId: string, period?: string) => ipcRenderer.invoke('get-analytics-data', restaurantId, period),
  // Notifications
  getNotifications: (userId: string, filters?: any) => ipcRenderer.invoke('get-notifications', userId, filters),
  createNotification: (notification: any) => ipcRenderer.invoke('create-notification', notification),
  markNotificationAsRead: (id: string) => ipcRenderer.invoke('mark-notification-as-read', id),
  markAllNotificationsAsRead: (userId: string) => ipcRenderer.invoke('mark-all-notifications-as-read', userId),
  deleteNotification: (id: string) => ipcRenderer.invoke('delete-notification', id),
  clearAllNotifications: (userId: string) => ipcRenderer.invoke('clear-all-notifications', userId),
  getUnreadNotificationCount: (userId: string) => ipcRenderer.invoke('get-unread-notification-count', userId),
  // Notification Preferences
  getNotificationPreferences: (userId: string) => ipcRenderer.invoke('get-notification-preferences', userId),
  updateNotificationPreference: (userId: string, category: string, preferences: any) => ipcRenderer.invoke('update-notification-preference', userId, category, preferences),
  // Notification Events
  onNotificationReceived: (callback: (notification: any) => void) => {
    ipcRenderer.on('notification-received', (_, notification) => callback(notification));
  },
  removeNotificationListener: () => {
    ipcRenderer.removeAllListeners('notification-received');
  },
  // Order Management
  createOrder: (orderData: any) => ipcRenderer.invoke('create-order', orderData),
  getOrders: (restaurantId: string, filters?: any) => ipcRenderer.invoke('get-orders', restaurantId, filters),
  updateOrder: (orderId: string, updates: any) => ipcRenderer.invoke('update-order', orderId, updates),
  updateOrderStatus: (orderId: string, status: string) => ipcRenderer.invoke('update-order-status', orderId, status),
  deleteOrder: (orderId: string) => ipcRenderer.invoke('delete-order', orderId),
  // Licensing and Subscription Management
  getSubscriptionStatus: (userId: string) => ipcRenderer.invoke('get-subscription-status', userId),
  checkFeatureAccess: (userId: string, featureId: string) => ipcRenderer.invoke('check-feature-access', userId, featureId),
  getAvailablePlans: () => ipcRenderer.invoke('get-available-plans'),
  upgradeSubscription: (userId: string, planType: string, paymentData: any) => ipcRenderer.invoke('upgrade-subscription', userId, planType, paymentData),
  getPaymentHistory: (userId: string) => ipcRenderer.invoke('get-payment-history', userId),

  // Backup Management
  createBackup: (filename?: string) => ipcRenderer.invoke('create-backup', filename),
  getBackups: () => ipcRenderer.invoke('get-backups'),
  restoreBackup: (backupId: string) => ipcRenderer.invoke('restore-backup', backupId),
  deleteBackup: (backupId: string) => ipcRenderer.invoke('delete-backup', backupId),
  getBackupStats: () => ipcRenderer.invoke('get-backup-stats'),
  exportData: (format: 'json' | 'csv', tables?: string[]) => ipcRenderer.invoke('export-data', format, tables),

  // Database Health
  databaseHealthCheck: () => ipcRenderer.invoke('database-health-check'),

  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),

  // POS Window Management
  createPOSWindow: (restaurantId: string, userDetails: any, restaurantDetails: any) =>
    ipcRenderer.invoke('create-pos-window', restaurantId, userDetails, restaurantDetails),
  closePOSWindow: (windowId: string) => ipcRenderer.invoke('close-pos-window', windowId),
  closePOSWindowSelf: () => ipcRenderer.invoke('close-pos-window-self'),
  getPOSWindows: () => ipcRenderer.invoke('get-pos-windows'),
  sendPOSMessage: (windowId: string, message: any) =>
    ipcRenderer.invoke('pos-window-message', windowId, message),
  broadcastToPOSWindows: (message: any) => ipcRenderer.invoke('broadcast-to-pos-windows', message),

  // POS Window Communication (for receiving messages)
  onPOSMessage: (callback: (message: any) => void) => {
    ipcRenderer.on('pos-message', (_, message) => callback(message));
  },
  onPOSBroadcast: (callback: (message: any) => void) => {
    ipcRenderer.on('pos-broadcast', (_, message) => callback(message));
  },
  removePOSListeners: () => {
    ipcRenderer.removeAllListeners('pos-message');
    ipcRenderer.removeAllListeners('pos-broadcast');
  }
});
