import React, { useState, useEffect } from 'react';
import { Table, Order } from '../types';
import BillingSystem from './BillingSystem';
import Icon from './Icon';
import { eventBus, EVENTS } from '../utils/eventBus';

interface TableManagementProps {
  restaurantId: string;
}

interface TableForm {
  number: string;
  capacity: string;
  area: string;
}

const TableManagement: React.FC<TableManagementProps> = ({ restaurantId }) => {
  const [tables, setTables] = useState<Table[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTable, setEditingTable] = useState<Table | null>(null);
  const [formData, setFormData] = useState<TableForm>({
    number: '',
    capacity: '',
    area: ''
  });

  // Pagination and filtering states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [searchTerm, setSearchTerm] = useState('');
  const [areaFilter, setAreaFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showTableOrders, setShowTableOrders] = useState(false);
  const [selectedTableForOrders, setSelectedTableForOrders] = useState<Table | null>(null);
  const [tableOrders, setTableOrders] = useState<Order[]>([]);
  const [showBilling, setShowBilling] = useState(false);
  const [billingOrder, setBillingOrder] = useState<Order | null>(null);

  const [showTableMenu, setShowTableMenu] = useState<string | null>(null);

  // Computed values for filtering and pagination
  const filteredTables = tables.filter(table => {
    const matchesSearch = (table.tableNumber || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (table.location || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesArea = areaFilter === 'all' || table.location === areaFilter;

    return matchesSearch && matchesArea;
  });

  const totalPages = Math.ceil(filteredTables.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTables = filteredTables.slice(startIndex, startIndex + itemsPerPage);

  // Get unique areas for filter
  const uniqueAreas = [...new Set(tables.map(table => table.location).filter(Boolean))];

  useEffect(() => {
    loadTables();
  }, [restaurantId]);

  // Listen for order completion events to update table status
  useEffect(() => {
    const handleOrderCompleted = async (order: Order) => {
      if (order.orderType === 'dine-in' && order.tableId) {
        // Set table back to available when order is completed
        await window.electronAPI.updateTable(order.tableId, { status: 'available' });
        await loadTables();
      }
    };

    eventBus.on(EVENTS.ORDER_UPDATED, handleOrderCompleted);
    eventBus.on(EVENTS.PAYMENT_COMPLETED, (data: any) => handleOrderCompleted(data.order));

    return () => {
      eventBus.off(EVENTS.ORDER_UPDATED, handleOrderCompleted);
      eventBus.off(EVENTS.PAYMENT_COMPLETED, handleOrderCompleted);
    };
  }, []);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowTableMenu(null);
    };

    if (showTableMenu) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showTableMenu]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, areaFilter]);

  const loadTables = async () => {
    try {
      setIsLoading(true);
      const data = await window.electronAPI.getTables(restaurantId);
      setTables(data);
    } catch (error) {
      console.error('Failed to load tables:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      number: '',
      capacity: '',
      area: ''
    });
    setEditingTable(null);
    setShowAddForm(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.number || !formData.capacity) {
      alert('Please fill in required fields');
      return;
    }

    const capacity = parseInt(formData.capacity);
    if (isNaN(capacity) || capacity <= 0) {
      alert('Please enter a valid capacity');
      return;
    }

    try {
      const tableData = {
        tableNumber: formData.number,
        capacity,
        status: 'available' as const, // Always default to available
        location: formData.area,
        restaurantId,
        isActive: true
      };

      let success = false;
      if (editingTable) {
        const result = await window.electronAPI.updateTable(editingTable.id, tableData);
        success = result.success;
        if (!success) {
          alert('Failed to update table: ' + result.error);
        }
      } else {
        const result = await window.electronAPI.createTable(tableData);
        success = result.success;
        if (!success) {
          alert('Failed to create table: ' + result.error);
        }
      }

      if (success) {
        await loadTables();
        resetForm();
      }
    } catch (error) {
      console.error('Error saving table:', error);
      alert('Failed to save table');
    }
  };

  const handleEdit = (table: Table) => {
    setFormData({
      number: table.tableNumber,
      capacity: table.capacity.toString(),
      area: table.location || ''
    });
    setEditingTable(table);
    setShowAddForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this table?')) {
      return;
    }

    try {
      const result = await window.electronAPI.deleteTable(id);
      if (result.success) {
        await loadTables();
      } else {
        alert('Failed to delete table: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting table:', error);
      alert('Failed to delete table');
    }
  };

  const handleTableClick = async (table: Table) => {
    if (table.status === 'occupied') {
      // Show current order details for occupied tables
      await handleViewTableOrders(table);
    }
  };



  const getStatusColor = (status: Table['status']) => {
    switch (status) {
      case 'available': return '#22c55e';
      case 'occupied': return '#ef4444';
      case 'reserved': return '#f59e0b';
      case 'maintenance': return '#6b7280';
      default: return '#22c55e';
    }
  };

  const loadTableOrders = async (tableId: string) => {
    try {
      const orders = await (window.electronAPI as any).getOrders(restaurantId, { tableId });
      setTableOrders(orders.filter((order: Order) => order.status !== 'completed' && order.status !== 'cancelled'));
    } catch (error) {
      console.error('Failed to load table orders:', error);
    }
  };

  const handleViewTableOrders = async (table: Table) => {
    setSelectedTableForOrders(table);
    await loadTableOrders(table.id);
    setShowTableOrders(true);
  };

  const handleBillOrder = (order: Order) => {
    setBillingOrder(order);
    setShowBilling(true);
  };

  const handlePaymentComplete = async () => {
    setShowBilling(false);
    setBillingOrder(null);
    if (selectedTableForOrders) {
      await loadTableOrders(selectedTableForOrders.id);
      await loadTables(); // Refresh table status
    }
  };

  if (isLoading) {
    return (
      <div className="table-management-loading">
        <div className="loading-spinner"></div>
        <p>Loading tables...</p>
      </div>
    );
  }

  return (
    <div className="table-management">
      <div className="table-management-header">
        <h2>Table Management</h2>
        <div className="header-actions">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            ➕ Add Table
          </button>
        </div>
      </div>

      {showAddForm && (
        <div className="table-form-modal">
          <div className="table-form-overlay" onClick={resetForm}></div>
          <div className="table-form-container">
            <div className="table-form-header">
              <h3>{editingTable ? 'Edit Table' : 'Add New Table'}</h3>
              <button className="close-btn" onClick={resetForm}>×</button>
            </div>
            
            <form onSubmit={handleSubmit} className="table-form">
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Table Number *</label>
                  <input
                    type="text"
                    className="form-input"
                    value={formData.number}
                    onChange={(e) => setFormData({...formData, number: e.target.value})}
                    placeholder="e.g., T1, A1, 101"
                    required
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Pax *</label>
                  <input
                    type="number"
                    min="1"
                    max="20"
                    className="form-input"
                    value={formData.capacity}
                    onChange={(e) => setFormData({...formData, capacity: e.target.value})}
                    placeholder="Number of persons"
                    required
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Area</label>
                  <input
                    type="text"
                    className="form-input"
                    value={formData.area}
                    onChange={(e) => setFormData({...formData, area: e.target.value})}
                    placeholder="e.g., Main Hall, Terrace, VIP"
                  />
                </div>
              </div>

              <div className="form-actions">
                <button type="button" className="btn btn-secondary" onClick={resetForm}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingTable ? 'Update' : 'Create'} Table
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Search and Filter Controls */}
      <div className="tables-controls">
        <div className="search-section">
          <div className="search-input-container">
            <input
              type="text"
              className="search-input"
              placeholder="Search tables by number or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <span className="search-icon">
              <Icon name="search" size="sm" />
            </span>
          </div>
        </div>

        <div className="filter-section">
          <div className="filter-group">
            <label className="filter-label">Area:</label>
            <select
              className="filter-select"
              value={areaFilter}
              onChange={(e) => setAreaFilter(e.target.value)}
            >
              <option value="all">All Areas</option>
              {uniqueAreas.map(area => (
                <option key={area} value={area}>
                  {area}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label className="filter-label">View:</label>
            <div className="view-toggle">
              <button
                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                onClick={() => setViewMode('grid')}
                title="Grid View"
              >
                <Icon name="grid" size="sm" />
              </button>
              <button
                className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
                onClick={() => setViewMode('list')}
                title="List View"
              >
                <Icon name="list" size="sm" />
              </button>
            </div>
          </div>

          <div className="filter-group">
            <label className="filter-label">Per Page:</label>
            <select
              className="filter-select"
              value={itemsPerPage}
              onChange={(e) => setItemsPerPage(Number(e.target.value))}
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>

        <div className="tables-info">
          <span className="results-count">
            Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredTables.length)} of {filteredTables.length} tables
          </span>
          {totalPages > 1 && (
            <div className="pagination">
              <button
                className="page-btn"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                ‹ Prev
              </button>
              <span className="page-info">
                Page {currentPage} of {totalPages}
              </span>
              <button
                className="page-btn"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next ›
              </button>
            </div>
          )}
        </div>
      </div>

      <div className={`tables-grid ${viewMode}`}>
        {tables.length === 0 ? (
          <div className="empty-tables">
            <span className="empty-icon">
              <Icon name="table" size="xl" />
            </span>
            <h3>No tables configured</h3>
            <p>Add your first table to start managing seating</p>
            <button
              className="btn btn-primary"
              onClick={() => setShowAddForm(true)}
            >
              Add Table
            </button>
          </div>
        ) : filteredTables.length === 0 ? (
          <div className="empty-tables">
            <span className="empty-icon">
              <Icon name="search" size="xl" />
            </span>
            <h3>No tables found</h3>
            <p>Try adjusting your search or filter criteria</p>
            <button
              className="btn btn-secondary"
              onClick={() => {
                setSearchTerm('');
                setAreaFilter('all');
              }}
            >
              Clear Filters
            </button>
          </div>
        ) : (
          paginatedTables.map(table => (
            <div
              key={table.id}
              className={`table-card-compact status-${table.status}`}
              onClick={() => handleTableClick(table)}
              style={{ borderColor: getStatusColor(table.status) }}
            >
              <div className="table-card-content">
                <div className="table-number">T{table.tableNumber}</div>
                <div className="table-pax">{table.capacity} pax</div>
                {table.location && <div className="table-area">{table.location}</div>}
              </div>

              <div className="table-menu">
                <button
                  className="menu-trigger"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowTableMenu(showTableMenu === table.id ? null : table.id);
                  }}
                >
                  ⋮
                </button>

                {showTableMenu === table.id && (
                  <div className="menu-dropdown">
                    <button
                      className="menu-item"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEdit(table);
                        setShowTableMenu(null);
                      }}
                    >
                      ✏️ Edit
                    </button>
                    <button
                      className="menu-item delete"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(table.id);
                        setShowTableMenu(null);
                      }}
                    >
                      🗑️ Delete
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {showTableOrders && selectedTableForOrders && (
        <div className="table-orders-modal">
          <div className="modal-overlay" onClick={() => setShowTableOrders(false)}></div>
          <div className="modal-content">
            <div className="modal-header">
              <h2>Orders for Table {selectedTableForOrders.tableNumber}</h2>
              <button className="close-btn" onClick={() => setShowTableOrders(false)}>×</button>
            </div>

            <div className="table-orders-content">
              {tableOrders.length === 0 ? (
                <div className="no-orders">
                  <p>No active orders for this table</p>
                </div>
              ) : (
                <div className="orders-list">
                  {tableOrders.map(order => (
                    <div key={order.id} className="order-summary-card">
                      <div className="order-header">
                        <span className="order-number">{order.orderNumber}</span>
                        <span className={`order-status status-${order.status}`}>
                          {order.status.toUpperCase()}
                        </span>
                      </div>

                      <div className="order-items">
                        {order.items.map(item => (
                          <div key={item.id} className="order-item-summary">
                            <span>{item.quantity}x {item.menuItemName}</span>
                            <span>₹{item.subtotal.toFixed(2)}</span>
                          </div>
                        ))}
                      </div>

                      <div className="order-total">
                        <strong>Total: ₹{order.totalAmount.toFixed(2)}</strong>
                      </div>

                      <div className="order-actions">
                        {order.status === 'served' && order.paymentStatus === 'pending' && (
                          <button
                            className="bill-btn"
                            onClick={() => handleBillOrder(order)}
                          >
                            💳 Process Payment
                          </button>
                        )}
                        <span className={`payment-status ${order.paymentStatus}`}>
                          Payment: {order.paymentStatus.toUpperCase()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {showBilling && billingOrder && (
        <BillingSystem
          order={billingOrder}
          onClose={() => {
            setShowBilling(false);
            setBillingOrder(null);
          }}
          onPaymentComplete={handlePaymentComplete}
        />
      )}
    </div>
  );
};

export default TableManagement;
