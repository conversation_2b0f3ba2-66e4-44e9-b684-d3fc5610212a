import * as path from 'path';
import * as fs from 'fs';
import { app } from 'electron';
import { AnalyticsConfig } from './analyticsService';

export interface AppConfig {
  analytics: AnalyticsConfig;
  version: string;
  lastUpdated: string;
}

export class ConfigService {
  private configPath: string;
  private config: AppConfig;

  constructor() {
    this.configPath = path.join(app.getPath('userData'), 'zyka-config.json');
    this.config = this.getDefaultConfig();
  }

  // Get default configuration
  private getDefaultConfig(): AppConfig {
    const analyticsEnabled = process.env.ANALYTICS_ENABLED !== 'false'; // Default to true unless explicitly disabled
    const measurementId = process.env.GA4_MEASUREMENT_ID || '';
    const apiSecret = process.env.GA4_API_SECRET || '';

    return {
      analytics: {
        measurementId,
        apiSecret,
        enabled: analyticsEnabled && measurementId !== '' && apiSecret !== '', // Only enable if credentials are provided
        syncInterval: process.env.ANALYTICS_SYNC_INTERVAL || '0 */6 * * *', // Every 6 hours by default
        lastSyncTimestamp: undefined
      },
      version: '1.0.0',
      lastUpdated: new Date().toISOString()
    };
  }

  // Load configuration from file
  async loadConfig(): Promise<AppConfig> {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        const loadedConfig = JSON.parse(configData);
        
        // Merge with defaults to ensure all properties exist
        this.config = {
          ...this.getDefaultConfig(),
          ...loadedConfig,
          analytics: {
            ...this.getDefaultConfig().analytics,
            ...loadedConfig.analytics
          }
        };
      } else {
        // Create default config file
        await this.saveConfig();
      }
    } catch (error) {
      console.error('Failed to load config:', error);
      this.config = this.getDefaultConfig();
    }

    return this.config;
  }

  // Save configuration to file
  async saveConfig(): Promise<void> {
    try {
      this.config.lastUpdated = new Date().toISOString();
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
      console.log('Configuration saved successfully');
    } catch (error) {
      console.error('Failed to save config:', error);
      throw error;
    }
  }

  // Get current configuration
  getConfig(): AppConfig {
    return { ...this.config };
  }

  // Update analytics configuration
  async updateAnalyticsConfig(analyticsConfig: Partial<AnalyticsConfig>): Promise<void> {
    this.config.analytics = {
      ...this.config.analytics,
      ...analyticsConfig
    };
    await this.saveConfig();
  }

  // Get analytics configuration
  getAnalyticsConfig(): AnalyticsConfig {
    return { ...this.config.analytics };
  }

  // Validate analytics configuration
  validateAnalyticsConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const analytics = this.config.analytics;

    if (!analytics.measurementId || analytics.measurementId.trim() === '') {
      errors.push('Measurement ID is required');
    }

    if (!analytics.apiSecret || analytics.apiSecret.trim() === '') {
      errors.push('API Secret is required');
    }

    if (!analytics.syncInterval || analytics.syncInterval.trim() === '') {
      errors.push('Sync interval is required');
    }

    // Validate measurement ID format (should start with G-)
    if (analytics.measurementId && !analytics.measurementId.startsWith('G-')) {
      errors.push('Measurement ID should start with "G-"');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Reset configuration to defaults
  async resetConfig(): Promise<void> {
    this.config = this.getDefaultConfig();
    await this.saveConfig();
  }

  // Get configuration file path
  getConfigPath(): string {
    return this.configPath;
  }
}

// Singleton instance
let configServiceInstance: ConfigService | null = null;

export function getConfigService(): ConfigService {
  if (!configServiceInstance) {
    configServiceInstance = new ConfigService();
  }
  return configServiceInstance;
}
