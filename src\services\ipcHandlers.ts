import { ipc<PERSON>ain } from 'electron';
import { getDatabaseService } from './databaseService';
import { getBackupService } from './backupService';
import { notificationService } from './notificationService';
import * as path from 'path';
import * as fs from 'fs';
import { app } from 'electron';

const databaseService = getDatabaseService();
const backupService = getBackupService();

// Menu Management IPC Handlers
export function setupMenuHandlers() {
  ipcMain.handle('get-menu-items', async (_, restaurantUserId?: string) => {
    try {
      let sql = `
        SELECT id, restaurant_id, name, price, description, category, image,
               available, is_deleted, calories, protein, carbs, fat,
               allergens, preparation_time, popularity, created_at, updated_at
        FROM menu_items
        WHERE is_deleted = 0
      `;
      const params: any[] = [];

      if (restaurantUserId) {
        // Get the actual restaurant ID from the user_id
        const restaurant = await databaseService.sqliteService.get(`
          SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
        `, [restaurantUserId]);

        if (restaurant) {
          sql += ' AND restaurant_id = ?';
          params.push(restaurant.id);
        } else {
          // If restaurant not found, return empty array
          return [];
        }
      }

      sql += ' ORDER BY category ASC, name ASC';

      const menuItems = await databaseService.sqliteService.all(sql, params);
      return menuItems;
    } catch (error) {
      console.error('Database error getting menu items:', error);
      return [];
    }
  });

  ipcMain.handle('create-menu-item', async (_, menuItem) => {
    try {
      // Get the actual restaurant ID from the user_id
      const restaurant = await databaseService.sqliteService.get(`
        SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
      `, [menuItem.restaurantId]);

      if (!restaurant) {
        return { success: false, error: `Restaurant not found for user ID: ${menuItem.restaurantId}` };
      }

      const actualRestaurantId = restaurant.id;
      const menuItemId = `menu_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const now = new Date().toISOString();

      await databaseService.sqliteService.run(`
        INSERT INTO menu_items (
          id, restaurant_id, name, price, description, category, image,
          available, is_deleted, calories, protein, carbs, fat,
          allergens, preparation_time, popularity, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        menuItemId,
        actualRestaurantId,
        menuItem.name,
        menuItem.price,
        menuItem.description || null,
        menuItem.category || null,
        menuItem.image || null,
        menuItem.available ? 1 : 0,
        0, // is_deleted
        menuItem.calories || null,
        menuItem.protein || null,
        menuItem.carbs || null,
        menuItem.fat || null,
        menuItem.allergens ? JSON.stringify(menuItem.allergens) : null,
        menuItem.preparationTime || 0,
        menuItem.popularity || 0,
        now,
        now
      ]);

      const createdMenuItem = await databaseService.sqliteService.get(
        'SELECT * FROM menu_items WHERE id = ?',
        [menuItemId]
      );

      return { success: true, menuItem: createdMenuItem };
    } catch (error) {
      console.error('Database error creating menu item:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('delete-menu-item', async (_, id) => {
    try {
      await databaseService.sqliteService.run(
        'UPDATE menu_items SET is_deleted = 1, updated_at = ? WHERE id = ?',
        [new Date().toISOString(), id]
      );

      return { success: true };
    } catch (error) {
      console.error('Database error deleting menu item:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('update-menu-item', async (_, id, updates) => {
    try {
      const setClause = [];
      const values = [];
      
      if (updates.name !== undefined) {
        setClause.push('name = ?');
        values.push(updates.name);
      }
      if (updates.price !== undefined) {
        setClause.push('price = ?');
        values.push(updates.price);
      }
      if (updates.description !== undefined) {
        setClause.push('description = ?');
        values.push(updates.description);
      }
      if (updates.category !== undefined) {
        setClause.push('category = ?');
        values.push(updates.category);
      }
      if (updates.image !== undefined) {
        setClause.push('image = ?');
        values.push(updates.image);
      }
      if (updates.available !== undefined) {
        setClause.push('available = ?');
        values.push(updates.available ? 1 : 0);
      }
      if (updates.calories !== undefined) {
        setClause.push('calories = ?');
        values.push(updates.calories);
      }
      if (updates.protein !== undefined) {
        setClause.push('protein = ?');
        values.push(updates.protein);
      }
      if (updates.carbs !== undefined) {
        setClause.push('carbs = ?');
        values.push(updates.carbs);
      }
      if (updates.fat !== undefined) {
        setClause.push('fat = ?');
        values.push(updates.fat);
      }
      if (updates.allergens !== undefined) {
        setClause.push('allergens = ?');
        values.push(updates.allergens ? JSON.stringify(updates.allergens) : null);
      }
      if (updates.preparationTime !== undefined) {
        setClause.push('preparation_time = ?');
        values.push(updates.preparationTime);
      }

      setClause.push('updated_at = ?');
      values.push(new Date().toISOString());
      values.push(id);

      await databaseService.sqliteService.run(
        `UPDATE menu_items SET ${setClause.join(', ')} WHERE id = ?`,
        values
      );

      return { success: true };
    } catch (error) {
      console.error('Database error updating menu item:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('bulk-import-menu-items', async (_, menuItems, restaurantUserId) => {
    try {
      // First, get the actual restaurant ID from the user_id
      const restaurant = await databaseService.sqliteService.get(`
        SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
      `, [restaurantUserId]);

      if (!restaurant) {
        return { success: false, error: `Restaurant not found for user ID: ${restaurantUserId}` };
      }

      const actualRestaurantId = restaurant.id;
      const timestamp = new Date().toISOString();

      // Use transaction for bulk import to ensure data consistency
      await databaseService.sqliteService.transaction(async () => {
        for (let i = 0; i < menuItems.length; i++) {
          const item = menuItems[i];
          const id = `menu_${Date.now()}_${i}_${Math.random().toString(36).substring(2, 11)}`;

          await databaseService.sqliteService.run(`
            INSERT INTO menu_items (
              id, restaurant_id, name, price, description, category, image,
              available, is_deleted, preparation_time, popularity, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            id, actualRestaurantId, item.name, item.price,
            item.description || null, item.category || null, item.image || null,
            item.available !== false ? 1 : 0, 0, // is_deleted = 0
            item.preparationTime || 0, 0, // popularity = 0
            timestamp, timestamp
          ]);
        }
      });

      return { success: true, imported: menuItems.length };
    } catch (error) {
      console.error('Database error bulk importing menu items:', error);
      return { success: false, error: (error as Error).message };
    }
  });
}

// Table Management IPC Handlers
export function setupTableHandlers() {
  ipcMain.handle('get-tables', async (_, restaurantUserId?: string) => {
    try {
      let sql = `
        SELECT id, restaurant_id, table_number, capacity, location, status,
               current_order_id, reservation_customer_name, reservation_customer_phone,
               reservation_time, reservation_party_size, created_at, updated_at
        FROM tables
        WHERE is_active = 1
      `;
      const params: any[] = [];

      if (restaurantUserId) {
        // Get the actual restaurant ID from the user_id
        const restaurant = await databaseService.sqliteService.get(`
          SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
        `, [restaurantUserId]);

        if (restaurant) {
          sql += ' AND restaurant_id = ?';
          params.push(restaurant.id);
        } else {
          // If restaurant not found, return empty array
          return [];
        }
      }

      sql += ' ORDER BY table_number';

      const tables = await databaseService.sqliteService.all(sql, params);

      return tables.map((table: any) => ({
        id: table.id,
        restaurantId: table.restaurant_id,
        tableNumber: table.table_number,
        capacity: table.capacity,
        location: table.location,
        status: table.status,
        currentOrderId: table.current_order_id,
        reservationCustomerName: table.reservation_customer_name,
        reservationCustomerPhone: table.reservation_customer_phone,
        reservationTime: table.reservation_time,
        reservationPartySize: table.reservation_party_size,
        createdAt: table.created_at,
        updatedAt: table.updated_at
      }));
    } catch (error) {
      console.error('Database error getting tables:', error);
      return [];
    }
  });

  ipcMain.handle('create-table', async (_, table) => {
    try {
      // Get the actual restaurant ID from the user_id
      const restaurant = await databaseService.sqliteService.get(`
        SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
      `, [table.restaurantId]);

      if (!restaurant) {
        return { success: false, error: `Restaurant not found for user ID: ${table.restaurantId}` };
      }

      const actualRestaurantId = restaurant.id;
      const id = `table_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const now = new Date().toISOString();

      await databaseService.sqliteService.run(`
        INSERT INTO tables (
          id, restaurant_id, table_number, capacity, location, status,
          current_order_id, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id, actualRestaurantId, table.tableNumber, table.capacity,
        table.location || null, table.status, table.currentOrderId || null,
        1, now, now
      ]);

      const newTable = {
        id,
        restaurantId: actualRestaurantId,
        tableNumber: table.tableNumber,
        capacity: table.capacity,
        location: table.location,
        status: table.status,
        currentOrderId: table.currentOrderId,
        createdAt: now,
        updatedAt: now
      };

      return { success: true, table: newTable };
    } catch (error) {
      console.error('Database error creating table:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('update-table', async (_, id, updates) => {
    try {
      const setClause = [];
      const values = [];
      
      if (updates.tableNumber !== undefined) {
        setClause.push('table_number = ?');
        values.push(updates.tableNumber);
      }
      if (updates.capacity !== undefined) {
        setClause.push('capacity = ?');
        values.push(updates.capacity);
      }
      if (updates.location !== undefined) {
        setClause.push('location = ?');
        values.push(updates.location);
      }
      if (updates.status !== undefined) {
        setClause.push('status = ?');
        values.push(updates.status);
      }
      if (updates.currentOrderId !== undefined) {
        setClause.push('current_order_id = ?');
        values.push(updates.currentOrderId);
      }

      setClause.push('updated_at = ?');
      values.push(new Date().toISOString());
      values.push(id);

      await databaseService.sqliteService.run(
        `UPDATE tables SET ${setClause.join(', ')} WHERE id = ?`,
        values
      );

      // Send notification for table status change
      if (updates.status !== undefined) {
        // Get table and restaurant info for notification
        const tableInfo = await databaseService.sqliteService.get(`
          SELECT t.table_number, r.user_id, r.id as restaurant_id
          FROM tables t
          JOIN restaurants r ON t.restaurant_id = r.id
          WHERE t.id = ?
        `, [id]);

        if (tableInfo) {
          await notificationService.notifyTableStatusChange(
            tableInfo.user_id,
            tableInfo.restaurant_id,
            tableInfo.table_number,
            updates.status
          );
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Database error updating table:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('delete-table', async (_, id) => {
    try {
      await databaseService.sqliteService.run(
        'UPDATE tables SET is_active = 0, updated_at = ? WHERE id = ?',
        [new Date().toISOString(), id]
      );
      return { success: true };
    } catch (error) {
      console.error('Database error deleting table:', error);
      return { success: false, error: (error as Error).message };
    }
  });
}

// Order Management IPC Handlers
export function setupOrderHandlers() {
  ipcMain.handle('create-order', async (_, orderData) => {
    try {
      // Get the actual restaurant ID from the user_id
      const restaurant = await databaseService.sqliteService.get(`
        SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
      `, [orderData.restaurantId]);

      if (!restaurant) {
        return { success: false, error: `Restaurant not found for user ID: ${orderData.restaurantId}` };
      }

      const actualRestaurantId = restaurant.id;
      const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const orderNumber = `ORD-${Date.now().toString().slice(-6)}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
      const now = new Date().toISOString();

      // Create order using the database service
      const order = await databaseService.createOrder({
        restaurantId: actualRestaurantId.toString(), // Convert to string to match interface
        tableId: orderData.tableId,
        tableNumber: orderData.tableNumber,
        orderType: orderData.orderType,
        status: orderData.status || 'pending',
        subtotal: orderData.subtotal,
        taxAmount: orderData.taxAmount,
        serviceChargeAmount: orderData.serviceChargeAmount || 0,
        discountAmount: orderData.discountAmount || 0,
        totalAmount: orderData.totalAmount,
        paymentStatus: orderData.paymentStatus || 'pending',
        paymentMethod: orderData.paymentMethod,
        customerName: orderData.customerName,
        customerPhone: orderData.customerPhone,
        customerEmail: orderData.customerEmail,
        notes: orderData.notes,
        kotPrinted: orderData.kotPrinted || false,
        billPrinted: orderData.billPrinted || false,
        orderSource: orderData.orderSource || 'pos',
        staffId: orderData.staffId,
        deviceId: orderData.deviceId,
        sessionId: orderData.sessionId
      }, orderData.items || []);

      // Send notification for new order
      await notificationService.notifyNewOrder(
        orderData.restaurantId, // This is the user_id
        actualRestaurantId,
        order.orderNumber,
        orderData.tableNumber
      );

      return { success: true, order };
    } catch (error) {
      console.error('Error creating order:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('get-orders', async (_, restaurantUserId: string, filters?: any) => {
    try {
      // Get the actual restaurant ID from the user_id
      const restaurant = await databaseService.sqliteService.get(`
        SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
      `, [restaurantUserId]);

      if (!restaurant) {
        console.error(`Restaurant not found for user ID: ${restaurantUserId}`);
        return [];
      }

      const actualRestaurantId = restaurant.id.toString();
      const orders = await databaseService.getOrdersByRestaurant(actualRestaurantId, filters);
      return orders;
    } catch (error) {
      console.error('Error getting orders:', error);
      return [];
    }
  });

  ipcMain.handle('update-order-status', async (_, orderId: string, status: string) => {
    try {
      const order = await databaseService.updateOrderStatus(orderId, status as any);
      return { success: true, order };
    } catch (error) {
      console.error('Error updating order status:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('update-order', async (_, orderId: string, updates: any) => {
    try {
      const setClause = [];
      const values = [];
      
      if (updates.kotPrinted !== undefined) {
        setClause.push('kot_printed = ?');
        values.push(updates.kotPrinted ? 1 : 0);
      }
      if (updates.billPrinted !== undefined) {
        setClause.push('bill_printed = ?');
        values.push(updates.billPrinted ? 1 : 0);
      }
      if (updates.paymentStatus !== undefined) {
        setClause.push('payment_status = ?');
        values.push(updates.paymentStatus);
      }
      if (updates.paymentMethod !== undefined) {
        setClause.push('payment_method = ?');
        values.push(updates.paymentMethod);
      }
      if (updates.status !== undefined) {
        setClause.push('status = ?');
        values.push(updates.status);

        // Set completion timestamp if status is completed
        if (updates.status === 'completed') {
          setClause.push('completed_at = ?');
          values.push(new Date().toISOString());
        }
      }

      setClause.push('updated_at = ?');
      values.push(new Date().toISOString());
      values.push(orderId);

      await databaseService.sqliteService.run(
        `UPDATE orders SET ${setClause.join(', ')} WHERE id = ?`,
        values
      );

      const order = await databaseService.getOrderById(orderId);
      return { success: true, order };
    } catch (error) {
      console.error('Error updating order:', error);
      return { success: false, error: (error as Error).message };
    }
  });
}

// Tax Management IPC Handlers
export function setupTaxHandlers() {
  // Helper function to map database row to TaxRate interface
  const mapTaxRateFromDb = (dbTaxRate: any): any => {
    return {
      id: dbTaxRate.id,
      name: dbTaxRate.tax_name,
      rate: dbTaxRate.tax_rate,
      type: dbTaxRate.tax_type,
      isDefault: dbTaxRate.is_default === 1,
      isActive: dbTaxRate.is_active === 1,
      restaurantId: dbTaxRate.restaurant_id,
      createdAt: dbTaxRate.created_at,
      updatedAt: dbTaxRate.updated_at
    };
  };

  ipcMain.handle('get-tax-rates', async (_, restaurantId?: string) => {
    try {
      let sql = `
        SELECT id, restaurant_id, tax_name, tax_rate, tax_type, is_default,
               is_active, created_at, updated_at
        FROM tax_rates
        WHERE is_active = 1
      `;
      const params: any[] = [];

      if (restaurantId) {
        // Get the actual restaurant ID from the user_id
        const restaurant = await databaseService.sqliteService.get(`
          SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
        `, [restaurantId]);

        if (restaurant) {
          sql += ' AND restaurant_id = ?';
          params.push(restaurant.id);
        } else {
          // If restaurant not found, return empty array
          return [];
        }
      }

      sql += ' ORDER BY is_default DESC, tax_name ASC';

      const taxRates = await databaseService.sqliteService.all(sql, params);
      return taxRates.map(mapTaxRateFromDb);
    } catch (error) {
      console.error('Database error getting tax rates:', error);
      return [];
    }
  });

  ipcMain.handle('create-tax-rate', async (_, taxRateData) => {
    try {
      // Get the actual restaurant ID from the user_id
      const restaurant = await databaseService.sqliteService.get(`
        SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
      `, [taxRateData.restaurantId]);

      if (!restaurant) {
        return { success: false, error: `Restaurant not found for user ID: ${taxRateData.restaurantId}` };
      }

      const taxRateId = `tax_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const now = new Date().toISOString();

      await databaseService.sqliteService.run(`
        INSERT INTO tax_rates (
          id, restaurant_id, tax_name, tax_rate, tax_type, is_default,
          is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        taxRateId,
        restaurant.id, // Use the actual restaurant database ID (integer)
        taxRateData.name,
        taxRateData.rate,
        taxRateData.type || 'percentage',
        taxRateData.isDefault || false,
        true,
        now,
        now
      ]);

      const taxRate = await databaseService.sqliteService.get(
        'SELECT * FROM tax_rates WHERE id = ?',
        [taxRateId]
      );

      // Send notification for new tax rate
      await notificationService.notifyTaxRateChange(
        taxRateData.restaurantId, // This is the user_id
        restaurant.id,
        taxRateData.name,
        0, // old rate (new tax rate)
        taxRateData.rate
      );

      return { success: true, taxRate: mapTaxRateFromDb(taxRate) };
    } catch (error) {
      console.error('Database error creating tax rate:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('update-tax-rate', async (_, id, updates) => {
    try {
      const now = new Date().toISOString();
      const setClause: string[] = [];
      const values: any[] = [];

      // Handle default tax logic - only one tax can be default per restaurant
      if (updates.isDefault === true) {
        // First get the restaurant ID for this tax
        const currentTax = await databaseService.sqliteService.get(
          'SELECT restaurant_id FROM tax_rates WHERE id = ?',
          [id]
        );

        if (currentTax) {
          // Remove default from all other taxes in this restaurant
          await databaseService.sqliteService.run(
            'UPDATE tax_rates SET is_default = 0, updated_at = ? WHERE restaurant_id = ? AND id != ?',
            [now, currentTax.restaurant_id, id]
          );
        }
      }

      // Build dynamic update query
      if (updates.name !== undefined) {
        setClause.push('tax_name = ?');
        values.push(updates.name);
      }
      if (updates.rate !== undefined) {
        setClause.push('tax_rate = ?');
        values.push(updates.rate);
      }
      if (updates.type !== undefined) {
        setClause.push('tax_type = ?');
        values.push(updates.type);
      }
      if (updates.isDefault !== undefined) {
        setClause.push('is_default = ?');
        values.push(updates.isDefault ? 1 : 0);
      }
      if (updates.isActive !== undefined) {
        setClause.push('is_active = ?');
        values.push(updates.isActive ? 1 : 0);
      }

      setClause.push('updated_at = ?');
      values.push(now);
      values.push(id);

      await databaseService.sqliteService.run(
        `UPDATE tax_rates SET ${setClause.join(', ')} WHERE id = ?`,
        values
      );

      const updatedTaxRate = await databaseService.sqliteService.get(
        'SELECT * FROM tax_rates WHERE id = ?',
        [id]
      );

      return { success: true, taxRate: mapTaxRateFromDb(updatedTaxRate) };
    } catch (error) {
      console.error('Database error updating tax rate:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('delete-tax-rate', async (_, id) => {
    try {
      // Soft delete by setting is_active to false
      await databaseService.sqliteService.run(
        'UPDATE tax_rates SET is_active = 0, updated_at = ? WHERE id = ?',
        [new Date().toISOString(), id]
      );

      return { success: true };
    } catch (error) {
      console.error('Database error deleting tax rate:', error);
      return { success: false, error: (error as Error).message };
    }
  });
}

// Subscription Management IPC Handlers
export function setupSubscriptionHandlers() {
  ipcMain.handle('get-subscription-status', async (_, userId?: string) => {
    try {
      let sql = `
        SELECT subscription_status, current_plan, subscription_end_date,
               trial_start_date, trial_end_date
        FROM users
        WHERE is_active = 1
      `;
      const params: any[] = [];

      if (userId) {
        sql += ' AND user_id = ?';
        params.push(userId);
      }

      sql += ' ORDER BY created_at DESC LIMIT 1';

      const user = await databaseService.sqliteService.get(sql, params);

      if (!user) {
        return {
          status: 'inactive',
          plan: 'none',
          subscriptionEndDate: null,
          trialStartDate: null,
          trialEndDate: null,
          daysRemaining: 0
        };
      }

      // Calculate days remaining
      let daysRemaining = 0;
      const now = new Date();

      if (user.subscription_status === 'trial' && user.trial_end_date) {
        const trialEnd = new Date(user.trial_end_date);
        const timeDiff = trialEnd.getTime() - now.getTime();
        daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));
      } else if (user.subscription_status === 'active' && user.subscription_end_date) {
        const subEnd = new Date(user.subscription_end_date);
        const timeDiff = subEnd.getTime() - now.getTime();
        daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));
      }

      return {
        status: user.subscription_status,
        plan: user.current_plan,
        subscriptionEndDate: user.subscription_end_date,
        trialStartDate: user.trial_start_date,
        trialEndDate: user.trial_end_date,
        daysRemaining: daysRemaining,
        nextBillingDate: user.subscription_end_date
      };
    } catch (error) {
      console.error('Database error getting subscription status:', error);
      return {
        subscriptionStatus: 'inactive',
        currentPlan: 'none',
        subscriptionEndDate: null,
        trialStartDate: null,
        trialEndDate: null
      };
    }
  });

  ipcMain.handle('check-feature-access', async (_, feature: string, userId?: string) => {
    try {
      // Get user subscription status
      const user = await databaseService.sqliteService.get(`
        SELECT subscription_status, current_plan, trial_end_date
        FROM users
        WHERE is_active = 1
        ORDER BY created_at DESC
        LIMIT 1
      `);

      if (!user) {
        return { hasAccess: false, reason: 'No user found' };
      }

      // Check if trial is still active
      const now = new Date();
      const trialEnd = user.trial_end_date ? new Date(user.trial_end_date) : null;
      const isTrialActive = trialEnd && now <= trialEnd;

      // Check if subscription is active
      const isSubscriptionActive = user.subscription_status === 'active';

      // Basic features are always available
      const basicFeatures = ['menu', 'pos', 'tables', 'orders'];
      if (basicFeatures.includes(feature)) {
        return { hasAccess: true, reason: 'Basic feature' };
      }

      // Premium features require active subscription or trial
      if (isTrialActive || isSubscriptionActive) {
        return { hasAccess: true, reason: isTrialActive ? 'Trial active' : 'Subscription active' };
      }

      return { hasAccess: false, reason: 'Subscription required' };
    } catch (error) {
      console.error('Error checking feature access:', error);
      return { hasAccess: false, reason: 'Error checking access' };
    }
  });
}

// Settings Management IPC Handlers
export function setupSettingsHandlers() {
  ipcMain.handle('get-billing-settings', async (_, restaurantId: string) => {
    try {
      // First try to find by user_id (string)
      let billingSettings = await databaseService.sqliteService.get(`
        SELECT * FROM billing_settings WHERE restaurant_id = ?
      `, [restaurantId]);

      // If not found and restaurantId looks like a number, try to find by database id
      if (!billingSettings && /^\d+$/.test(restaurantId)) {
        // Get the user_id for this restaurant database id
        const restaurant = await databaseService.sqliteService.get(`
          SELECT user_id FROM restaurants WHERE id = ? AND is_active = 1
        `, [parseInt(restaurantId)]);

        if (restaurant) {
          billingSettings = await databaseService.sqliteService.get(`
            SELECT * FROM billing_settings WHERE restaurant_id = ?
          `, [restaurant.user_id]);
        }
      }

      if (!billingSettings) {
        return null; // Will use default settings
      }

      // Convert database format to application format
      return {
        id: billingSettings.id,
        restaurantId: billingSettings.restaurant_id,
        header: {
          showLogo: Boolean(billingSettings.header_show_logo),
          logoUrl: billingSettings.header_logo_url || '',
          restaurantName: billingSettings.header_restaurant_name || '',
          address: billingSettings.header_address || '',
          phone: billingSettings.header_phone || '',
          email: billingSettings.header_email || '',
          website: billingSettings.header_website || '',
          gstNumber: billingSettings.header_gst_number || '',
          customText: billingSettings.header_custom_text || '',
        },
        footer: {
          thankYouMessage: billingSettings.footer_thank_you_message || 'Thank you for your visit!',
          termsAndConditions: billingSettings.footer_terms_and_conditions || '',
          customText: billingSettings.footer_custom_text || '',
          showQRCode: Boolean(billingSettings.footer_show_qr_code),
          qrCodeData: billingSettings.footer_qr_code_data || '',
        },
        format: {
          paperSize: billingSettings.format_paper_size || 'thermal_80mm',
          fontSize: billingSettings.format_font_size || 'medium',
          showItemImages: Boolean(billingSettings.format_show_item_images),
          showTaxBreakdown: Boolean(billingSettings.format_show_tax_breakdown),
        },
        printer: {
          printerName: billingSettings.printer_name || '',
          autoprint: Boolean(billingSettings.printer_autoprint),
          copies: billingSettings.printer_copies || 1,
        },
        updatedAt: billingSettings.updated_at
      };
    } catch (error) {
      console.error('Error getting billing settings:', error);
      return null;
    }
  });

  ipcMain.handle('save-billing-settings', async (_, settings) => {
    try {
      const settingsId = settings.id || `billing_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const now = new Date().toISOString();

      // Check if settings already exist
      const existingSettings = await databaseService.sqliteService.get(`
        SELECT id FROM billing_settings WHERE restaurant_id = ?
      `, [settings.restaurantId]);

      if (existingSettings) {
        // Update existing settings
        await databaseService.sqliteService.run(`
          UPDATE billing_settings SET
            header_show_logo = ?,
            header_logo_url = ?,
            header_restaurant_name = ?,
            header_address = ?,
            header_phone = ?,
            header_email = ?,
            header_website = ?,
            header_gst_number = ?,
            header_custom_text = ?,
            footer_thank_you_message = ?,
            footer_terms_and_conditions = ?,
            footer_custom_text = ?,
            footer_show_qr_code = ?,
            footer_qr_code_data = ?,
            format_paper_size = ?,
            format_font_size = ?,
            format_show_item_images = ?,
            format_show_tax_breakdown = ?,
            printer_name = ?,
            printer_autoprint = ?,
            printer_copies = ?,
            updated_at = ?
          WHERE restaurant_id = ?
        `, [
          settings.header.showLogo ? 1 : 0,
          settings.header.logoUrl || null,
          settings.header.restaurantName || null,
          settings.header.address || null,
          settings.header.phone || null,
          settings.header.email || null,
          settings.header.website || null,
          settings.header.gstNumber || null,
          settings.header.customText || null,
          settings.footer.thankYouMessage || 'Thank you for your visit!',
          settings.footer.termsAndConditions || null,
          settings.footer.customText || null,
          settings.footer.showQRCode ? 1 : 0,
          settings.footer.qrCodeData || null,
          settings.format.paperSize || 'thermal_80mm',
          settings.format.fontSize || 'medium',
          settings.format.showItemImages ? 1 : 0,
          settings.format.showTaxBreakdown ? 1 : 0,
          settings.printer.printerName || null,
          settings.printer.autoprint ? 1 : 0,
          settings.printer.copies || 1,
          now,
          settings.restaurantId
        ]);
      } else {
        // Insert new settings
        await databaseService.sqliteService.run(`
          INSERT INTO billing_settings (
            id, restaurant_id, header_show_logo, header_logo_url, header_restaurant_name,
            header_address, header_phone, header_email, header_website, header_gst_number,
            header_custom_text, footer_thank_you_message, footer_terms_and_conditions,
            footer_custom_text, footer_show_qr_code, footer_qr_code_data, format_paper_size,
            format_font_size, format_show_item_images, format_show_tax_breakdown,
            printer_name, printer_autoprint, printer_copies, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          settingsId,
          settings.restaurantId,
          settings.header.showLogo ? 1 : 0,
          settings.header.logoUrl || null,
          settings.header.restaurantName || null,
          settings.header.address || null,
          settings.header.phone || null,
          settings.header.email || null,
          settings.header.website || null,
          settings.header.gstNumber || null,
          settings.header.customText || null,
          settings.footer.thankYouMessage || 'Thank you for your visit!',
          settings.footer.termsAndConditions || null,
          settings.footer.customText || null,
          settings.footer.showQRCode ? 1 : 0,
          settings.footer.qrCodeData || null,
          settings.format.paperSize || 'thermal_80mm',
          settings.format.fontSize || 'medium',
          settings.format.showItemImages ? 1 : 0,
          settings.format.showTaxBreakdown ? 1 : 0,
          settings.printer.printerName || null,
          settings.printer.autoprint ? 1 : 0,
          settings.printer.copies || 1,
          now,
          now
        ]);
      }

      // Return the updated settings
      const updatedSettings = { ...settings, id: settingsId, updatedAt: now };
      return { success: true, settings: updatedSettings };
    } catch (error) {
      console.error('Error saving billing settings:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  ipcMain.handle('get-app-settings', async (_, restaurantId: string) => {
    try {
      const appSettings = await databaseService.sqliteService.get(`
        SELECT * FROM app_settings WHERE restaurant_id = ?
      `, [restaurantId]);

      if (!appSettings) {
        return null; // Will use default settings
      }

      // Convert database format to application format
      return {
        id: appSettings.id,
        restaurantId: appSettings.restaurant_id,
        general: {
          currency: appSettings.general_currency || 'INR',
          currencySymbol: appSettings.general_currency_symbol || '₹',
          language: appSettings.general_language || 'en',
          timezone: appSettings.general_timezone || 'Asia/Kolkata',
          dateFormat: appSettings.general_date_format || 'DD/MM/YYYY',
          timeFormat: appSettings.general_time_format || '12h',
        },
        pos: {
          autoSaveOrders: Boolean(appSettings.pos_auto_save_orders),
          soundEnabled: Boolean(appSettings.pos_sound_enabled),
          showItemImages: Boolean(appSettings.pos_show_item_images),
          defaultTaxRate: appSettings.pos_default_tax_rate || undefined,
        },
        updatedAt: appSettings.updated_at
      };
    } catch (error) {
      console.error('Error getting app settings:', error);
      return null;
    }
  });

  ipcMain.handle('save-app-settings', async (_, settings) => {
    try {
      const settingsId = settings.id || `app_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const now = new Date().toISOString();

      // Check if settings already exist
      const existingSettings = await databaseService.sqliteService.get(`
        SELECT id FROM app_settings WHERE restaurant_id = ?
      `, [settings.restaurantId]);

      if (existingSettings) {
        // Update existing settings
        await databaseService.sqliteService.run(`
          UPDATE app_settings SET
            general_currency = ?,
            general_currency_symbol = ?,
            general_language = ?,
            general_timezone = ?,
            general_date_format = ?,
            general_time_format = ?,
            pos_auto_save_orders = ?,
            pos_sound_enabled = ?,
            pos_show_item_images = ?,
            pos_default_tax_rate = ?,
            updated_at = ?
          WHERE restaurant_id = ?
        `, [
          settings.general.currency || 'INR',
          settings.general.currencySymbol || '₹',
          settings.general.language || 'en',
          settings.general.timezone || 'Asia/Kolkata',
          settings.general.dateFormat || 'DD/MM/YYYY',
          settings.general.timeFormat || '12h',
          settings.pos.autoSaveOrders ? 1 : 0,
          settings.pos.soundEnabled ? 1 : 0,
          settings.pos.showItemImages ? 1 : 0,
          settings.pos.defaultTaxRate || null,
          now,
          settings.restaurantId
        ]);
      } else {
        // Insert new settings
        await databaseService.sqliteService.run(`
          INSERT INTO app_settings (
            id, restaurant_id, general_currency, general_currency_symbol, general_language,
            general_timezone, general_date_format, general_time_format, pos_auto_save_orders,
            pos_sound_enabled, pos_show_item_images, pos_default_tax_rate, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          settingsId,
          settings.restaurantId,
          settings.general.currency || 'INR',
          settings.general.currencySymbol || '₹',
          settings.general.language || 'en',
          settings.general.timezone || 'Asia/Kolkata',
          settings.general.dateFormat || 'DD/MM/YYYY',
          settings.general.timeFormat || '12h',
          settings.pos.autoSaveOrders ? 1 : 0,
          settings.pos.soundEnabled ? 1 : 0,
          settings.pos.showItemImages ? 1 : 0,
          settings.pos.defaultTaxRate || null,
          now,
          now
        ]);
      }

      // Return the updated settings
      const updatedSettings = { ...settings, id: settingsId, updatedAt: now };
      return { success: true, settings: updatedSettings };
    } catch (error) {
      console.error('Error saving app settings:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });
}

// Payment Management IPC Handlers
export function setupPaymentHandlers() {
  ipcMain.handle('get-payment-history', async (_, restaurantId?: string) => {
    try {
      let sql = `
        SELECT pt.id, pt.user_id, pt.subscription_id, pt.amount, pt.currency,
               pt.payment_method, pt.status, pt.transaction_id, pt.gateway_response,
               pt.created_at, pt.updated_at,
               u.full_name as user_name, u.email as user_email
        FROM payment_transactions pt
        LEFT JOIN users u ON pt.user_id = u.user_id
        WHERE 1=1
      `;
      const params: any[] = [];

      if (restaurantId) {
        sql += ' AND pt.user_id = ?';
        params.push(restaurantId);
      }

      sql += ' ORDER BY pt.created_at DESC';

      const payments = await databaseService.sqliteService.all(sql, params);
      return payments;
    } catch (error) {
      console.error('Database error getting payment history:', error);
      return [];
    }
  });
}

// Image Management IPC Handlers
export function setupImageHandlers() {
  // Get the images directory path
  const getImagesDir = () => {
    const userDataPath = app.getPath('userData');
    const imagesDir = path.join(userDataPath, 'images');

    // Create images directory if it doesn't exist
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true });
    }

    return imagesDir;
  };

  ipcMain.handle('save-image', async (_, imageData: string, fileName: string) => {
    try {
      // Remove data URL prefix if present
      const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');

      // Generate unique filename
      const timestamp = Date.now();
      const extension = path.extname(fileName) || '.jpg';
      const uniqueFileName = `${timestamp}_${Math.random().toString(36).substring(2, 11)}${extension}`;

      const imagesDir = getImagesDir();
      const filePath = path.join(imagesDir, uniqueFileName);

      // Save the file
      fs.writeFileSync(filePath, base64Data, 'base64');

      // Return relative path for storage in database
      const relativePath = `images/${uniqueFileName}`;

      return { success: true, filePath: relativePath };
    } catch (error) {
      console.error('Error saving image:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('get-image-path', async (_, relativePath: string) => {
    try {
      const userDataPath = app.getPath('userData');
      const fullPath = path.join(userDataPath, relativePath);

      // Check if file exists
      if (fs.existsSync(fullPath)) {
        return { success: true, fullPath };
      } else {
        return { success: false, error: 'Image file not found' };
      }
    } catch (error) {
      console.error('Error getting image path:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('delete-image', async (_, relativePath: string) => {
    try {
      const userDataPath = app.getPath('userData');
      const fullPath = path.join(userDataPath, relativePath);

      // Check if file exists and delete it
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
        return { success: true };
      } else {
        return { success: true }; // File doesn't exist, consider it deleted
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      return { success: false, error: (error as Error).message };
    }
  });
}

// Analytics IPC Handlers
export function setupAnalyticsHandlers() {
  ipcMain.handle('get-analytics-data', async (_, restaurantUserId: string, period: string = 'today') => {
    try {
      // Get the actual restaurant ID from the user_id
      const restaurant = await databaseService.sqliteService.get(`
        SELECT id FROM restaurants WHERE user_id = ? AND is_active = 1
      `, [restaurantUserId]);

      if (!restaurant) {
        console.error(`Restaurant not found for user ID: ${restaurantUserId}`);
        return null;
      }

      const actualRestaurantId = restaurant.id.toString();

      // Get basic counts
      const [tables, menuItems, orders] = await Promise.all([
        databaseService.sqliteService.all('SELECT * FROM tables WHERE restaurant_id = ?', [actualRestaurantId]),
        databaseService.sqliteService.all('SELECT * FROM menu_items WHERE restaurant_id = ? AND is_deleted = 0 AND available = 1', [actualRestaurantId]),
        databaseService.sqliteService.all('SELECT * FROM orders WHERE restaurant_id = ?', [actualRestaurantId])
      ]);

      // Calculate date ranges
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Filter orders based on period
      let filteredOrders = orders;
      if (period === 'today') {
        filteredOrders = orders.filter((order: any) => new Date(order.created_at) >= today);
      } else if (period === 'week') {
        filteredOrders = orders.filter((order: any) => new Date(order.created_at) >= weekAgo);
      } else if (period === 'month') {
        filteredOrders = orders.filter((order: any) => new Date(order.created_at) >= monthAgo);
      }

      // Calculate metrics
      const totalRevenue = orders.reduce((sum: number, order: any) => sum + (order.total_amount || 0), 0);
      const periodRevenue = filteredOrders.reduce((sum: number, order: any) => sum + (order.total_amount || 0), 0);
      const activeOrders = orders.filter((order: any) => order.status === 'active' || order.status === 'preparing').length;
      const completedOrders = orders.filter((order: any) => order.status === 'completed').length;
      const cancelledOrders = orders.filter((order: any) => order.status === 'cancelled').length;

      // Calculate average order value
      const averageOrderValue = filteredOrders.length > 0 ? periodRevenue / filteredOrders.length : 0;

      // Calculate hourly distribution for today
      const todayOrders = orders.filter((order: any) => new Date(order.created_at) >= today);
      const hourlyDistribution: { [key: string]: number } = {};
      todayOrders.forEach((order: any) => {
        const hour = new Date(order.created_at).getHours();
        const hourKey = `${hour}:00`;
        hourlyDistribution[hourKey] = (hourlyDistribution[hourKey] || 0) + 1;
      });

      const busyHours = Object.entries(hourlyDistribution)
        .map(([hour, count]) => ({ hour, orders: count }))
        .sort((a, b) => b.orders - a.orders)
        .slice(0, 5);

      // Calculate daily revenue for the last 7 days
      const dailyRevenue = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
        const dayOrders = orders.filter((order: any) => {
          const orderDate = new Date(order.created_at);
          return orderDate.toDateString() === date.toDateString();
        });
        const revenue = dayOrders.reduce((sum: number, order: any) => sum + (order.total_amount || 0), 0);
        dailyRevenue.push({
          day: date.toLocaleDateString('en-US', { weekday: 'short' }),
          revenue
        });
      }

      // Get popular items from order_items
      let popularItems: Array<{
        name: string;
        quantity: number;
        revenue: number;
        orderCount: number;
      }> = [];
      try {
        const orderItems = await databaseService.sqliteService.all(`
          SELECT
            oi.menu_item_name,
            SUM(oi.quantity) as total_quantity,
            SUM(oi.subtotal) as total_revenue,
            COUNT(DISTINCT oi.order_id) as order_count
          FROM order_items oi
          JOIN orders o ON oi.order_id = o.id
          WHERE o.restaurant_id = ? AND o.created_at >= ?
          GROUP BY oi.menu_item_name
          ORDER BY total_quantity DESC
          LIMIT 10
        `, [actualRestaurantId, period === 'today' ? today.toISOString() :
            period === 'week' ? weekAgo.toISOString() :
            monthAgo.toISOString()]);

        popularItems = orderItems.map((item: any) => ({
          name: item.menu_item_name,
          quantity: item.total_quantity,
          revenue: item.total_revenue,
          orderCount: item.order_count
        }));
      } catch (error) {
        console.error('Error getting popular items:', error);
        // Fallback to empty array if order_items table doesn't exist yet
        popularItems = [];
      }

      // Table occupancy
      const occupiedTables = tables.filter((table: any) => table.status === 'occupied').length;
      const occupancyRate = tables.length > 0 ? (occupiedTables / tables.length) * 100 : 0;

      // Calculate most popular table
      let mostPopularTable = 'N/A';
      try {
        const tablePopularity = await databaseService.sqliteService.get(`
          SELECT table_number, COUNT(*) as order_count
          FROM orders
          WHERE restaurant_id = ? AND table_number IS NOT NULL
          GROUP BY table_number
          ORDER BY order_count DESC
          LIMIT 1
        `, [actualRestaurantId]);

        if (tablePopularity) {
          mostPopularTable = `Table ${tablePopularity.table_number}`;
        }
      } catch (error) {
        console.error('Error getting most popular table:', error);
      }

      return {
        overview: {
          totalTables: tables.length,
          totalMenuItems: menuItems.length,
          totalOrders: orders.length,
          totalRevenue,
          activeOrders,
          completedOrders,
          cancelledOrders
        },
        periodStats: {
          ordersInPeriod: filteredOrders.length,
          revenueInPeriod: periodRevenue,
          averageOrderValue,
          busyHours
        },
        weeklyStats: {
          dailyRevenue,
          popularItems
        },
        tableStats: {
          occupancyRate,
          averageTurnover: filteredOrders.length > 0 && occupiedTables > 0 ?
            (filteredOrders.length / Math.max(occupiedTables, 1)) : 0,
          mostPopularTable
        }
      };
    } catch (error) {
      console.error('Error getting analytics data:', error);
      return null;
    }
  });
}

// Notification Management IPC Handlers
export function setupNotificationHandlers() {
  ipcMain.handle('get-notifications', async (_, userId: string, filters?: { type?: string; unreadOnly?: boolean; limit?: number }) => {
    try {
      let sql = `
        SELECT id, user_id, restaurant_id, type, title, message, data,
               read_status, priority, auto_dismiss, dismiss_after,
               created_at, read_at
        FROM notifications
        WHERE user_id = ?
      `;
      const params: any[] = [userId];

      if (filters?.type) {
        sql += ' AND type = ?';
        params.push(filters.type);
      }

      if (filters?.unreadOnly) {
        sql += ' AND read_status = 0';
      }

      sql += ' ORDER BY created_at DESC';

      if (filters?.limit) {
        sql += ' LIMIT ?';
        params.push(filters.limit);
      }

      const notifications = await databaseService.sqliteService.all(sql, params);

      return notifications.map((notif: any) => ({
        id: notif.id,
        userId: notif.user_id,
        restaurantId: notif.restaurant_id,
        type: notif.type,
        title: notif.title,
        message: notif.message,
        data: notif.data ? JSON.parse(notif.data) : null,
        readStatus: Boolean(notif.read_status),
        priority: notif.priority,
        autoDismiss: Boolean(notif.auto_dismiss),
        dismissAfter: notif.dismiss_after,
        createdAt: notif.created_at,
        readAt: notif.read_at
      }));
    } catch (error) {
      console.error('Database error getting notifications:', error);
      return [];
    }
  });

  ipcMain.handle('create-notification', async (_, notificationData) => {
    try {
      const notificationId = `notif_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const now = new Date().toISOString();

      await databaseService.sqliteService.run(`
        INSERT INTO notifications (
          id, user_id, restaurant_id, type, title, message, data,
          read_status, priority, auto_dismiss, dismiss_after, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        notificationId,
        notificationData.userId,
        notificationData.restaurantId || null,
        notificationData.type,
        notificationData.title,
        notificationData.message,
        notificationData.data ? JSON.stringify(notificationData.data) : null,
        notificationData.readStatus ? 1 : 0,
        notificationData.priority || 'normal',
        notificationData.autoDismiss ? 1 : 0,
        notificationData.dismissAfter || 0,
        now
      ]);

      const notification = await databaseService.sqliteService.get(
        'SELECT * FROM notifications WHERE id = ?',
        [notificationId]
      );

      const formattedNotification = {
        id: notification.id,
        userId: notification.user_id,
        restaurantId: notification.restaurant_id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data ? JSON.parse(notification.data) : null,
        readStatus: Boolean(notification.read_status),
        priority: notification.priority,
        autoDismiss: Boolean(notification.auto_dismiss),
        dismissAfter: notification.dismiss_after,
        createdAt: notification.created_at,
        readAt: notification.read_at
      };

      // Emit notification event to all renderer processes
      const { BrowserWindow } = require('electron');
      BrowserWindow.getAllWindows().forEach((window: any) => {
        window.webContents.send('notification-received', formattedNotification);
      });

      return { success: true, notification: formattedNotification };
    } catch (error) {
      console.error('Database error creating notification:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('mark-notification-as-read', async (_, id: string) => {
    try {
      const now = new Date().toISOString();
      await databaseService.sqliteService.run(
        'UPDATE notifications SET read_status = 1, read_at = ? WHERE id = ?',
        [now, id]
      );
      return { success: true };
    } catch (error) {
      console.error('Database error marking notification as read:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('mark-all-notifications-as-read', async (_, userId: string) => {
    try {
      const now = new Date().toISOString();
      await databaseService.sqliteService.run(
        'UPDATE notifications SET read_status = 1, read_at = ? WHERE user_id = ? AND read_status = 0',
        [now, userId]
      );
      return { success: true };
    } catch (error) {
      console.error('Database error marking all notifications as read:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('delete-notification', async (_, id: string) => {
    try {
      await databaseService.sqliteService.run(
        'DELETE FROM notifications WHERE id = ?',
        [id]
      );
      return { success: true };
    } catch (error) {
      console.error('Database error deleting notification:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('clear-all-notifications', async (_, userId: string) => {
    try {
      await databaseService.sqliteService.run(
        'DELETE FROM notifications WHERE user_id = ?',
        [userId]
      );
      return { success: true };
    } catch (error) {
      console.error('Database error clearing all notifications:', error);
      return { success: false, error: (error as Error).message };
    }
  });

  ipcMain.handle('get-unread-notification-count', async (_, userId: string) => {
    try {
      const result = await databaseService.sqliteService.get(
        'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND read_status = 0',
        [userId]
      );
      return result?.count || 0;
    } catch (error) {
      console.error('Database error getting unread notification count:', error);
      return 0;
    }
  });

  ipcMain.handle('get-notification-preferences', async (_, userId: string) => {
    try {
      const preferences = await databaseService.sqliteService.all(`
        SELECT id, user_id, category, enabled, sound_enabled, desktop_enabled,
               toast_enabled, auto_dismiss_enabled, auto_dismiss_time, frequency,
               created_at, updated_at
        FROM notification_preferences
        WHERE user_id = ?
        ORDER BY category
      `, [userId]);

      return preferences.map((pref: any) => ({
        id: pref.id,
        userId: pref.user_id,
        category: pref.category,
        enabled: Boolean(pref.enabled),
        soundEnabled: Boolean(pref.sound_enabled),
        desktopEnabled: Boolean(pref.desktop_enabled),
        toastEnabled: Boolean(pref.toast_enabled),
        autoDismissEnabled: Boolean(pref.auto_dismiss_enabled),
        autoDismissTime: pref.auto_dismiss_time,
        frequency: pref.frequency,
        createdAt: pref.created_at,
        updatedAt: pref.updated_at
      }));
    } catch (error) {
      console.error('Database error getting notification preferences:', error);
      return [];
    }
  });

  ipcMain.handle('update-notification-preference', async (_, userId: string, category: string, preferences: any) => {
    try {
      const now = new Date().toISOString();
      const setClause: string[] = [];
      const values: any[] = [];

      if (preferences.enabled !== undefined) {
        setClause.push('enabled = ?');
        values.push(preferences.enabled ? 1 : 0);
      }
      if (preferences.soundEnabled !== undefined) {
        setClause.push('sound_enabled = ?');
        values.push(preferences.soundEnabled ? 1 : 0);
      }
      if (preferences.desktopEnabled !== undefined) {
        setClause.push('desktop_enabled = ?');
        values.push(preferences.desktopEnabled ? 1 : 0);
      }
      if (preferences.toastEnabled !== undefined) {
        setClause.push('toast_enabled = ?');
        values.push(preferences.toastEnabled ? 1 : 0);
      }
      if (preferences.autoDismissEnabled !== undefined) {
        setClause.push('auto_dismiss_enabled = ?');
        values.push(preferences.autoDismissEnabled ? 1 : 0);
      }
      if (preferences.autoDismissTime !== undefined) {
        setClause.push('auto_dismiss_time = ?');
        values.push(preferences.autoDismissTime);
      }
      if (preferences.frequency !== undefined) {
        setClause.push('frequency = ?');
        values.push(preferences.frequency);
      }

      setClause.push('updated_at = ?');
      values.push(now);
      values.push(userId);
      values.push(category);

      await databaseService.sqliteService.run(
        `UPDATE notification_preferences SET ${setClause.join(', ')} WHERE user_id = ? AND category = ?`,
        values
      );

      return { success: true };
    } catch (error) {
      console.error('Database error updating notification preference:', error);
      return { success: false, error: (error as Error).message };
    }
  });
}

// Initialize all handlers
export function setupAllIpcHandlers() {
  setupMenuHandlers();
  setupTableHandlers();
  setupOrderHandlers();
  setupTaxHandlers();
  setupSubscriptionHandlers();
  setupSettingsHandlers();
  setupAnalyticsHandlers();
  setupPaymentHandlers();
  setupImageHandlers();
  setupNotificationHandlers();
}
