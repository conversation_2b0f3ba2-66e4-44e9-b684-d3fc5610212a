import React from 'react';
import { UserDetails } from '../../types';

interface SubscriptionTabProps {
  userDetails: UserDetails;
}

const SubscriptionTab: React.FC<SubscriptionTabProps> = ({ userDetails }) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not available';
    return new Date(dateString).toLocaleDateString();
  };

  const getSubscriptionStatusBadge = () => {
    const status = userDetails.subscriptionStatus || 'trial';
    const statusConfig = {
      trial: { label: 'Free Trial', className: 'status-trial', color: '#f59e0b' },
      active: { label: 'Active', className: 'status-active', color: '#10b981' },
      expired: { label: 'Expired', className: 'status-expired', color: '#ef4444' },
      cancelled: { label: 'Cancelled', className: 'status-cancelled', color: '#6b7280' }
    };

    const config = statusConfig[status] || statusConfig.trial;
    return <span className={`status-badge ${config.className}`}>{config.label}</span>;
  };

  const getDaysRemaining = () => {
    if (!userDetails.trialEndDate && !userDetails.subscriptionEndDate) return null;
    
    const endDate = new Date(userDetails.trialEndDate || userDetails.subscriptionEndDate || '');
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  const daysRemaining = getDaysRemaining();

  return (
    <div className="subscription-tab">
      {/* Current Subscription Status */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Current Subscription</h3>
        </div>

        <div className="subscription-card">
          <div className="subscription-header">
            <div className="subscription-info">
              <h4 className="subscription-plan">
                {userDetails.subscriptionStatus === 'trial' ? 'Free Trial' : 
                 userDetails.currentPlan?.toUpperCase() || 'Basic Plan'}
              </h4>
              {getSubscriptionStatusBadge()}
            </div>
            <div className="subscription-price">
              {userDetails.subscriptionStatus === 'trial' ? 'FREE' : '₹999/month'}
            </div>
          </div>

          <div className="subscription-details">
            <div className="detail-row">
              <span className="detail-label">Status:</span>
              <span className="detail-value">{getSubscriptionStatusBadge()}</span>
            </div>

            {userDetails.subscriptionStatus === 'trial' && (
              <>
                <div className="detail-row">
                  <span className="detail-label">Trial Started:</span>
                  <span className="detail-value">{formatDate(userDetails.trialStartDate)}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">Trial Ends:</span>
                  <span className="detail-value">{formatDate(userDetails.trialEndDate)}</span>
                </div>
                {daysRemaining !== null && (
                  <div className="detail-row">
                    <span className="detail-label">Days Remaining:</span>
                    <span className={`detail-value ${daysRemaining <= 3 ? 'text-warning' : ''}`}>
                      {daysRemaining > 0 ? `${daysRemaining} days` : 'Expired'}
                    </span>
                  </div>
                )}
              </>
            )}

            {userDetails.subscriptionStatus === 'active' && userDetails.subscriptionEndDate && (
              <div className="detail-row">
                <span className="detail-label">Next Billing:</span>
                <span className="detail-value">{formatDate(userDetails.subscriptionEndDate)}</span>
              </div>
            )}
          </div>

          {userDetails.subscriptionStatus === 'trial' && daysRemaining !== null && daysRemaining <= 7 && (
            <div className="trial-warning">
              <h4>Trial Ending Soon!</h4>
              <p>Your free trial will end in {daysRemaining > 0 ? `${daysRemaining} days` : 'today'}. 
                 Upgrade now to continue using all features.</p>
            </div>
          )}
        </div>
      </div>

      {/* Available Plans */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Available Plans</h3>
        </div>

        <div className="plans-grid">
          <div className="plan-card basic">
            <div className="plan-header">
              <h4 className="plan-name">Basic Plan</h4>
              <div className="plan-price">
                <span className="price">₹999</span>
                <span className="period">/month</span>
              </div>
            </div>
            <div className="plan-features">
              <ul>
                <li>Order Management</li>
                <li>Kitchen Order Tickets (KOT)</li>
                <li>Basic Billing</li>
                <li>Customer Management</li>
                <li>Basic Reports</li>
                <li>Email Support</li>
              </ul>
            </div>
            <div className="plan-actions">
              <button className="btn btn-primary">
                {userDetails.subscriptionStatus === 'trial' ? 'Upgrade to Basic' : 'Current Plan'}
              </button>
            </div>
          </div>

          <div className="plan-card premium recommended">
            <div className="plan-badge">Recommended</div>
            <div className="plan-header">
              <h4 className="plan-name">Premium Plan</h4>
              <div className="plan-price">
                <span className="price">₹1,999</span>
                <span className="period">/month</span>
              </div>
            </div>
            <div className="plan-features">
              <ul>
                <li>All Basic Plan features</li>
                <li>Advanced Menu Management</li>
                <li>Table Management & Reservations</li>
                <li>Inventory Management</li>
                <li>Advanced Analytics & Reports</li>
                <li>Multi-location Support</li>
                <li>Priority Support</li>
                <li>Custom Branding</li>
              </ul>
            </div>
            <div className="plan-actions">
              <button className="btn btn-primary">
                Upgrade to Premium
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Billing History */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Billing History</h3>
        </div>

        <div className="billing-history">
          <div className="history-item">
            <div className="history-date">No billing history available</div>
            <div className="history-description">
              {userDetails.subscriptionStatus === 'trial' 
                ? 'You are currently on a free trial. Billing will start after your trial ends.'
                : 'No previous transactions found.'}
            </div>
          </div>
        </div>
      </div>

      {/* Support & Contact */}
      <div className="settings-section">
        <div className="section-header">
          <h3 className="section-title">Support & Contact</h3>
        </div>

        <div className="support-info">
          <div className="support-card">
            <h4>Need Help?</h4>
            <p>Our support team is here to help you with any questions about your subscription or the application.</p>
            <div className="support-actions">
              <button className="btn btn-secondary">
                Contact Support
              </button>
              <button className="btn btn-secondary">
                View Documentation
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionTab;
