import React, { useState } from 'react';
import { SubscriptionPlan } from '../../types';
import './PaymentForm.css';

interface PaymentFormProps {
  plan: SubscriptionPlan;
  userId: string;
  onPaymentSuccess: (paymentData: any) => void;
  onCancel: () => void;
}

type PaymentMethod = 'upi' | 'card' | 'netbanking' | 'wallet';

interface PaymentFormData {
  method: PaymentMethod;
  upiId?: string;
  cardNumber?: string;
  cardExpiry?: string;
  cardCvv?: string;
  cardName?: string;
  bankName?: string;
  walletType?: string;
  autoRenew: boolean;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  plan,
  userId,
  onPaymentSuccess,
  onCancel
}) => {
  const [formData, setFormData] = useState<PaymentFormData>({
    method: 'upi',
    autoRenew: false
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const paymentMethods = [
    { id: 'upi', name: 'UPI', icon: '📱', description: 'Pay using UPI ID' },
    { id: 'card', name: 'Credit/Debit Card', icon: '💳', description: 'Visa, Mastercard, RuPay' },
    { id: 'netbanking', name: 'Net Banking', icon: '🏦', description: 'All major banks' },
    { id: 'wallet', name: 'Digital Wallet', icon: '💰', description: 'Paytm, PhonePe, etc.' }
  ];

  const banks = [
    'State Bank of India', 'HDFC Bank', 'ICICI Bank', 'Axis Bank', 'Kotak Mahindra Bank',
    'Punjab National Bank', 'Bank of Baroda', 'Canara Bank', 'Union Bank of India', 'Other'
  ];

  const wallets = [
    'Paytm', 'PhonePe', 'Google Pay', 'Amazon Pay', 'Mobikwik', 'Freecharge', 'Other'
  ];

  const handleInputChange = (field: keyof PaymentFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    switch (formData.method) {
      case 'upi':
        if (!formData.upiId) {
          newErrors.upiId = 'UPI ID is required';
        } else if (!/^[\w.-]+@[\w.-]+$/.test(formData.upiId)) {
          newErrors.upiId = 'Please enter a valid UPI ID';
        }
        break;

      case 'card':
        if (!formData.cardNumber) {
          newErrors.cardNumber = 'Card number is required';
        } else if (!/^\d{16}$/.test(formData.cardNumber.replace(/\s/g, ''))) {
          newErrors.cardNumber = 'Please enter a valid 16-digit card number';
        }
        
        if (!formData.cardExpiry) {
          newErrors.cardExpiry = 'Expiry date is required';
        } else if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(formData.cardExpiry)) {
          newErrors.cardExpiry = 'Please enter expiry in MM/YY format';
        }
        
        if (!formData.cardCvv) {
          newErrors.cardCvv = 'CVV is required';
        } else if (!/^\d{3,4}$/.test(formData.cardCvv)) {
          newErrors.cardCvv = 'Please enter a valid CVV';
        }
        
        if (!formData.cardName) {
          newErrors.cardName = 'Cardholder name is required';
        }
        break;

      case 'netbanking':
        if (!formData.bankName) {
          newErrors.bankName = 'Please select your bank';
        }
        break;

      case 'wallet':
        if (!formData.walletType) {
          newErrors.walletType = 'Please select your wallet';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsProcessing(true);

    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate mock transaction ID
      const transactionId = `TXN${Date.now()}${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

      const paymentData = {
        ...formData,
        transactionId,
        amount: plan.price,
        currency: plan.currency,
        planId: plan.id,
        timestamp: new Date().toISOString()
      };

      // Call the upgrade subscription API
      const result = await window.electronAPI.upgradeSubscription(userId, plan.id, paymentData);

      if (result.success) {
        onPaymentSuccess({
          ...paymentData,
          subscription: result.subscription,
          transaction: result.transaction
        });
      } else {
        throw new Error(result.error || 'Payment failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      setErrors({ general: 'Payment failed. Please try again.' });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiry = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  return (
    <div className="payment-form-overlay">
      <div className="payment-form-container">
        <div className="payment-header">
          <h2>Complete Your Payment</h2>
          <button className="close-btn" onClick={onCancel}>×</button>
        </div>

        <div className="plan-summary">
          <div className="plan-info">
            <h3>{plan.name}</h3>
            <p>{plan.description}</p>
          </div>
          <div className="plan-price">
            <span className="amount">₹{plan.price}</span>
            <span className="period">/{plan.interval}</span>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="payment-form">
          <div className="payment-methods">
            <h4>Select Payment Method</h4>
            <div className="method-grid">
              {paymentMethods.map((method) => (
                <label
                  key={method.id}
                  className={`method-option ${formData.method === method.id ? 'selected' : ''}`}
                >
                  <input
                    type="radio"
                    name="paymentMethod"
                    value={method.id}
                    checked={formData.method === method.id}
                    onChange={(e) => handleInputChange('method', e.target.value as PaymentMethod)}
                  />
                  <div className="method-content">
                    <span className="method-icon">{method.icon}</span>
                    <div className="method-details">
                      <span className="method-name">{method.name}</span>
                      <span className="method-description">{method.description}</span>
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <div className="payment-details">
            {formData.method === 'upi' && (
              <div className="form-group">
                <label>UPI ID</label>
                <input
                  type="text"
                  placeholder="yourname@upi"
                  value={formData.upiId || ''}
                  onChange={(e) => handleInputChange('upiId', e.target.value)}
                  className={errors.upiId ? 'error' : ''}
                />
                {errors.upiId && <span className="error-message">{errors.upiId}</span>}
              </div>
            )}

            {formData.method === 'card' && (
              <>
                <div className="form-group">
                  <label>Card Number</label>
                  <input
                    type="text"
                    placeholder="1234 5678 9012 3456"
                    value={formData.cardNumber || ''}
                    onChange={(e) => handleInputChange('cardNumber', formatCardNumber(e.target.value))}
                    maxLength={19}
                    className={errors.cardNumber ? 'error' : ''}
                  />
                  {errors.cardNumber && <span className="error-message">{errors.cardNumber}</span>}
                </div>
                
                <div className="form-row">
                  <div className="form-group">
                    <label>Expiry Date</label>
                    <input
                      type="text"
                      placeholder="MM/YY"
                      value={formData.cardExpiry || ''}
                      onChange={(e) => handleInputChange('cardExpiry', formatExpiry(e.target.value))}
                      maxLength={5}
                      className={errors.cardExpiry ? 'error' : ''}
                    />
                    {errors.cardExpiry && <span className="error-message">{errors.cardExpiry}</span>}
                  </div>
                  
                  <div className="form-group">
                    <label>CVV</label>
                    <input
                      type="text"
                      placeholder="123"
                      value={formData.cardCvv || ''}
                      onChange={(e) => handleInputChange('cardCvv', e.target.value.replace(/\D/g, ''))}
                      maxLength={4}
                      className={errors.cardCvv ? 'error' : ''}
                    />
                    {errors.cardCvv && <span className="error-message">{errors.cardCvv}</span>}
                  </div>
                </div>
                
                <div className="form-group">
                  <label>Cardholder Name</label>
                  <input
                    type="text"
                    placeholder="John Doe"
                    value={formData.cardName || ''}
                    onChange={(e) => handleInputChange('cardName', e.target.value)}
                    className={errors.cardName ? 'error' : ''}
                  />
                  {errors.cardName && <span className="error-message">{errors.cardName}</span>}
                </div>
              </>
            )}

            {formData.method === 'netbanking' && (
              <div className="form-group">
                <label>Select Your Bank</label>
                <select
                  value={formData.bankName || ''}
                  onChange={(e) => handleInputChange('bankName', e.target.value)}
                  className={errors.bankName ? 'error' : ''}
                >
                  <option value="">Choose your bank</option>
                  {banks.map((bank) => (
                    <option key={bank} value={bank}>{bank}</option>
                  ))}
                </select>
                {errors.bankName && <span className="error-message">{errors.bankName}</span>}
              </div>
            )}

            {formData.method === 'wallet' && (
              <div className="form-group">
                <label>Select Your Wallet</label>
                <select
                  value={formData.walletType || ''}
                  onChange={(e) => handleInputChange('walletType', e.target.value)}
                  className={errors.walletType ? 'error' : ''}
                >
                  <option value="">Choose your wallet</option>
                  {wallets.map((wallet) => (
                    <option key={wallet} value={wallet}>{wallet}</option>
                  ))}
                </select>
                {errors.walletType && <span className="error-message">{errors.walletType}</span>}
              </div>
            )}
          </div>

          <div className="auto-renew-option">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={formData.autoRenew}
                onChange={(e) => handleInputChange('autoRenew', e.target.checked)}
              />
              <span className="checkmark"></span>
              Enable auto-renewal for uninterrupted service
            </label>
          </div>

          {errors.general && (
            <div className="error-message general-error">{errors.general}</div>
          )}

          <div className="form-actions">
            <button type="button" className="btn btn-secondary" onClick={onCancel}>
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <span className="loading-spinner"></span>
                  Processing...
                </>
              ) : (
                `Pay ₹${plan.price}`
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PaymentForm;
