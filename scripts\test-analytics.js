/**
 * Test script for Zyka POS Analytics Integration
 * This script validates that the analytics service is working correctly
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Test configuration
const TEST_CONFIG = {
  measurementId: process.env.GA4_MEASUREMENT_ID,
  apiSecret: process.env.GA4_API_SECRET,
  enabled: process.env.ANALYTICS_ENABLED !== 'false'
};

// Test data
const TEST_EVENTS = [
  {
    name: 'user_registered',
    params: {
      user_id: 'test_user_123',
      subscription_status: 'trial',
      current_plan: 'basic',
      trial_start_date: new Date().toISOString(),
      registration_date: new Date().toISOString(),
      user_type: 'restaurant_owner',
      user_email: '<EMAIL>',
      user_name: 'Test User',
      user_phone: '+1234567890'
    }
  },
  {
    name: 'restaurant_created',
    params: {
      user_id: 'test_user_123',
      restaurant_name: 'Test Restaurant',
      restaurant_type: 'Café',
      location: 'Test City',
      machine_code: 'TEST-MACHINE-001',
      creation_date: new Date().toISOString()
    }
  },
  {
    name: 'subscription_created',
    params: {
      user_id: 'test_user_123',
      subscription_id: 'test_sub_456',
      plan_type: 'basic',
      status: 'active',
      start_date: new Date().toISOString(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      auto_renew: true,
      payment_method: 'card'
    }
  },
  {
    name: 'purchase',
    params: {
      user_id: 'test_user_123',
      transaction_id: 'test_txn_789',
      value: 1500,
      currency: 'INR',
      payment_method: 'card',
      item_category: 'subscription'
    }
  }
];

// Generate client ID
function generateClientId() {
  const os = require('os');
  const crypto = require('crypto');
  const machineId = `${os.hostname()}-${os.platform()}-${os.arch()}`;
  return crypto.createHash('md5').update(machineId).digest('hex');
}

// Send test events to GA4
async function sendTestEvents() {
  if (!TEST_CONFIG.enabled) {
    console.log('❌ Analytics is disabled');
    return false;
  }

  if (!TEST_CONFIG.measurementId || !TEST_CONFIG.apiSecret) {
    console.log('❌ Missing GA4 credentials');
    console.log('Please set GA4_MEASUREMENT_ID and GA4_API_SECRET in your .env file');
    return false;
  }

  console.log('🧪 Testing Analytics Integration...');
  console.log(`📊 Measurement ID: ${TEST_CONFIG.measurementId}`);
  console.log(`🔑 API Secret: ${TEST_CONFIG.apiSecret.substring(0, 8)}...`);

  try {
    const payload = {
      client_id: generateClientId(),
      events: TEST_EVENTS
    };

    const response = await axios.post(
      `https://www.google-analytics.com/mp/collect?measurement_id=${TEST_CONFIG.measurementId}&api_secret=${TEST_CONFIG.apiSecret}`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    if (response.status === 204) {
      console.log('✅ Test events sent successfully!');
      console.log(`📈 Sent ${TEST_EVENTS.length} test events to GA4`);
      console.log('');
      console.log('📋 Test Events Sent:');
      TEST_EVENTS.forEach((event, index) => {
        console.log(`   ${index + 1}. ${event.name}`);
      });
      console.log('');
      console.log('⏰ Data should appear in GA4 within 24-48 hours');
      console.log('🔍 Check GA4 Realtime reports for immediate validation');
      return true;
    } else {
      console.log(`❌ Unexpected response status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Failed to send test events:');
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Data: ${JSON.stringify(error.response.data)}`);
    } else {
      console.log(`   Error: ${error.message}`);
    }
    return false;
  }
}

// Validate configuration
function validateConfig() {
  console.log('🔧 Validating Configuration...');
  
  const issues = [];
  
  if (!TEST_CONFIG.measurementId) {
    issues.push('GA4_MEASUREMENT_ID is not set');
  } else if (!TEST_CONFIG.measurementId.startsWith('G-')) {
    issues.push('GA4_MEASUREMENT_ID should start with "G-"');
  }
  
  if (!TEST_CONFIG.apiSecret) {
    issues.push('GA4_API_SECRET is not set');
  }
  
  if (!TEST_CONFIG.enabled) {
    issues.push('ANALYTICS_ENABLED is set to false');
  }
  
  if (issues.length > 0) {
    console.log('❌ Configuration Issues:');
    issues.forEach(issue => console.log(`   - ${issue}`));
    return false;
  }
  
  console.log('✅ Configuration is valid');
  return true;
}

// Main test function
async function runTests() {
  console.log('🚀 Zyka POS Analytics Test Suite');
  console.log('=====================================');
  console.log('');
  
  // Validate configuration
  const configValid = validateConfig();
  console.log('');
  
  if (!configValid) {
    console.log('❌ Please fix configuration issues before testing');
    process.exit(1);
  }
  
  // Send test events
  const testPassed = await sendTestEvents();
  console.log('');
  
  if (testPassed) {
    console.log('🎉 All tests passed!');
    console.log('');
    console.log('📝 Next Steps:');
    console.log('   1. Check GA4 Realtime reports in 5-10 minutes');
    console.log('   2. Set up your business dashboards');
    console.log('   3. Configure alerts for key metrics');
    console.log('   4. Deploy your application with analytics enabled');
    process.exit(0);
  } else {
    console.log('❌ Tests failed');
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('   1. Verify your GA4 credentials are correct');
    console.log('   2. Check that your GA4 property is active');
    console.log('   3. Ensure the API secret belongs to the correct data stream');
    console.log('   4. Try regenerating the API secret in GA4');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test suite crashed:', error);
  process.exit(1);
});
