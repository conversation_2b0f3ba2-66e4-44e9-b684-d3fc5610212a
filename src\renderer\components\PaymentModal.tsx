import React, { useState } from 'react';

interface PaymentModalProps {
  isOpen: boolean;
  total: number;
  onClose: () => void;
  onPaymentComplete: (paymentMethod: string, amountPaid: number) => void;
}

type PaymentMethod = 'cash' | 'card' | 'digital';

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  total,
  onClose,
  onPaymentComplete
}) => {
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [amountReceived, setAmountReceived] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen) return null;

  const calculateChange = () => {
    const received = parseFloat(amountReceived) || 0;
    return Math.max(0, received - total);
  };

  const handlePayment = async () => {
    setIsProcessing(true);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const amountPaid = paymentMethod === 'cash' ? parseFloat(amountReceived) || total : total;
    onPaymentComplete(paymentMethod, amountPaid);
    
    setIsProcessing(false);
    setAmountReceived('');
    onClose();
  };

  const isValidPayment = () => {
    if (paymentMethod === 'cash') {
      const received = parseFloat(amountReceived) || 0;
      return received >= total;
    }
    return true; // Card and digital payments are always valid
  };

  return (
    <div className="modal-overlay">
      <div className="payment-modal">
        <div className="modal-header">
          <h2>Process Payment</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="modal-content">
          <div className="payment-summary">
            <div className="total-amount">
              Total: ₹{total.toFixed(2)}
            </div>
          </div>

          <div className="payment-methods">
            <h3>Payment Method</h3>
            <div className="method-buttons">
              <button
                className={`method-btn ${paymentMethod === 'cash' ? 'active' : ''}`}
                onClick={() => setPaymentMethod('cash')}
              >
                💵 Cash
              </button>
              <button
                className={`method-btn ${paymentMethod === 'card' ? 'active' : ''}`}
                onClick={() => setPaymentMethod('card')}
              >
                💳 Card
              </button>
              <button
                className={`method-btn ${paymentMethod === 'digital' ? 'active' : ''}`}
                onClick={() => setPaymentMethod('digital')}
              >
                📱 Digital
              </button>
            </div>
          </div>

          {paymentMethod === 'cash' && (
            <div className="cash-payment">
              <div className="form-group">
                <label>Amount Received</label>
                <input
                  type="number"
                  step="0.01"
                  min={total}
                  value={amountReceived}
                  onChange={(e) => setAmountReceived(e.target.value)}
                  placeholder={`Minimum: ₹${total.toFixed(2)}`}
                  className="amount-input"
                />
              </div>
              
              {amountReceived && (
                <div className="change-display">
                  <div className="change-amount">
                    Change: ₹{calculateChange().toFixed(2)}
                  </div>
                </div>
              )}
            </div>
          )}

          {paymentMethod === 'card' && (
            <div className="card-payment">
              <div className="card-instructions">
                <p>💳 Insert or tap card on the terminal</p>
                <p>Follow the prompts on the card reader</p>
              </div>
            </div>
          )}

          {paymentMethod === 'digital' && (
            <div className="digital-payment">
              <div className="digital-instructions">
                <p>📱 Show QR code to customer</p>
                <p>Or enter customer's digital wallet ID</p>
              </div>
            </div>
          )}
        </div>

        <div className="modal-actions">
          <button className="cancel-btn" onClick={onClose}>
            Cancel
          </button>
          <button
            className="process-btn"
            onClick={handlePayment}
            disabled={!isValidPayment() || isProcessing}
          >
            {isProcessing ? 'Processing...' : `Process Payment`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
