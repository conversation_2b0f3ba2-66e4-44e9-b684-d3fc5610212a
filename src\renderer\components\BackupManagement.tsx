import React, { useState, useEffect } from 'react';
import './BackupManagement.css';

interface BackupInfo {
  id: string;
  filename: string;
  filePath: string;
  size: number;
  type: 'manual' | 'automatic' | 'scheduled';
  status: 'completed' | 'failed' | 'in_progress';
  createdAt: string;
}

interface BackupStats {
  totalBackups: number;
  totalSize: number;
  oldestBackup?: string;
  newestBackup?: string;
  lastAutomaticBackup?: string;
}

const BackupManagement: React.FC = () => {
  const [backups, setBackups] = useState<BackupInfo[]>([]);
  const [stats, setStats] = useState<BackupStats>({ totalBackups: 0, totalSize: 0 });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    loadBackups();
    loadStats();
  }, []);

  const loadBackups = async () => {
    try {
      const backupList = await window.electronAPI.getBackups();
      setBackups(backupList);
    } catch (error) {
      console.error('Failed to load backups:', error);
      setMessage({ type: 'error', text: 'Failed to load backups' });
    }
  };

  const loadStats = async () => {
    try {
      const backupStats = await window.electronAPI.getBackupStats();
      setStats(backupStats);
    } catch (error) {
      console.error('Failed to load backup stats:', error);
    }
  };

  const createBackup = async () => {
    setIsLoading(true);
    setMessage(null);
    
    try {
      const result = await window.electronAPI.createBackup();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Backup created successfully!' });
        await loadBackups();
        await loadStats();
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to create backup' });
      }
    } catch (error) {
      console.error('Error creating backup:', error);
      setMessage({ type: 'error', text: 'Failed to create backup' });
    } finally {
      setIsLoading(false);
    }
  };

  const restoreBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to restore this backup? This will replace all current data.')) {
      return;
    }

    setIsLoading(true);
    setMessage(null);
    
    try {
      const result = await window.electronAPI.restoreBackup(backupId);
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Backup restored successfully! Please restart the application.' });
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to restore backup' });
      }
    } catch (error) {
      console.error('Error restoring backup:', error);
      setMessage({ type: 'error', text: 'Failed to restore backup' });
    } finally {
      setIsLoading(false);
    }
  };

  const deleteBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await window.electronAPI.deleteBackup(backupId);
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Backup deleted successfully!' });
        await loadBackups();
        await loadStats();
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to delete backup' });
      }
    } catch (error) {
      console.error('Error deleting backup:', error);
      setMessage({ type: 'error', text: 'Failed to delete backup' });
    }
  };

  const exportData = async (format: 'json' | 'csv') => {
    setIsLoading(true);
    setMessage(null);
    
    try {
      const result = await window.electronAPI.exportData(format);
      
      if (result.success) {
        setMessage({ type: 'success', text: `Data exported successfully to ${result.filePath}` });
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to export data' });
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      setMessage({ type: 'error', text: 'Failed to export data' });
    } finally {
      setIsLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="backup-management">
      <div className="backup-header">
        <h2>Backup Management</h2>
        <div className="backup-actions">
          <button 
            className="create-backup-btn"
            onClick={createBackup}
            disabled={isLoading}
          >
            {isLoading ? 'Creating...' : '📦 Create Backup'}
          </button>
          <button 
            className="export-btn"
            onClick={() => exportData('json')}
            disabled={isLoading}
          >
            📄 Export JSON
          </button>
          <button 
            className="export-btn"
            onClick={() => exportData('csv')}
            disabled={isLoading}
          >
            📊 Export CSV
          </button>
        </div>
      </div>

      {message && (
        <div className={`message ${message.type}`}>
          {message.text}
        </div>
      )}

      <div className="backup-stats">
        <div className="stat-card">
          <h3>Total Backups</h3>
          <p className="stat-value">{stats.totalBackups}</p>
        </div>
        <div className="stat-card">
          <h3>Total Size</h3>
          <p className="stat-value">{formatFileSize(stats.totalSize)}</p>
        </div>
        <div className="stat-card">
          <h3>Latest Backup</h3>
          <p className="stat-value">
            {stats.newestBackup ? formatDate(stats.newestBackup) : 'None'}
          </p>
        </div>
        <div className="stat-card">
          <h3>Last Auto Backup</h3>
          <p className="stat-value">
            {stats.lastAutomaticBackup ? formatDate(stats.lastAutomaticBackup) : 'None'}
          </p>
        </div>
      </div>

      <div className="backup-list">
        <h3>Available Backups</h3>
        {backups.length === 0 ? (
          <div className="no-backups">
            <p>No backups found. Create your first backup to get started.</p>
          </div>
        ) : (
          <div className="backup-table">
            <div className="backup-table-header">
              <div className="col-filename">Filename</div>
              <div className="col-type">Type</div>
              <div className="col-size">Size</div>
              <div className="col-date">Created</div>
              <div className="col-actions">Actions</div>
            </div>
            {backups.map((backup) => (
              <div key={backup.id} className="backup-row">
                <div className="col-filename">
                  <span className="filename">{backup.filename}</span>
                </div>
                <div className="col-type">
                  <span className={`type-badge ${backup.type}`}>
                    {backup.type}
                  </span>
                </div>
                <div className="col-size">
                  {formatFileSize(backup.size)}
                </div>
                <div className="col-date">
                  {formatDate(backup.createdAt)}
                </div>
                <div className="col-actions">
                  <button
                    className="restore-btn"
                    onClick={() => restoreBackup(backup.id)}
                    disabled={isLoading}
                  >
                    🔄 Restore
                  </button>
                  <button
                    className="delete-btn"
                    onClick={() => deleteBackup(backup.id)}
                    disabled={isLoading}
                  >
                    🗑️ Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="backup-info">
        <h3>About Backups</h3>
        <ul>
          <li><strong>Manual Backups:</strong> Created on-demand by clicking "Create Backup"</li>
          <li><strong>Automatic Backups:</strong> Created automatically on schedule (daily/weekly/monthly)</li>
          <li><strong>Restore:</strong> Replace current data with backup data (requires restart)</li>
          <li><strong>Export:</strong> Export data in JSON or CSV format for external use</li>
        </ul>
      </div>
    </div>
  );
};

export default BackupManagement;
