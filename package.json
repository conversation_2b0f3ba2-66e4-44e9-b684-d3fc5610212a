{"name": "zyka", "version": "1.2.0", "description": "Touch-friendly POS desktop software", "main": "dist/main.js", "homepage": "./", "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development", "start": "electron .", "dev": "webpack --mode development --watch", "electron:dev": "npm run build:dev && electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "postinstall": "electron-builder install-app-deps", "test:analytics": "node scripts/test-analytics.js", "clean": "rimraf dist release node_modules/.cache", "clean:all": "rimraf dist release node_modules", "lint": "echo '<PERSON><PERSON> not configured yet'", "format": "echo 'Formatting not configured yet'"}, "keywords": ["pos", "desktop", "electron", "typescript", "touch"], "author": "Zyka Team", "license": "MIT", "build": {"appId": "com.zyka.pos", "productName": "Zyka POS", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "dependencies": {"axios": "^1.10.0", "dotenv": "^17.2.0", "electron": "^37.2.3", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "react": "^19.1.0", "react-dom": "^19.1.0", "sqlite3": "^5.1.7"}, "devDependencies": {"@types/node": "^24.0.14", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "css-loader": "^7.1.2", "electron-builder": "^26.0.12", "html-webpack-plugin": "^5.6.3", "rimraf": "^6.0.1", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.100.2", "webpack-cli": "^6.0.1"}}