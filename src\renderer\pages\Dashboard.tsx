import React, { useState, useEffect } from 'react';
import { UserDetails, RestaurantDetails, MenuItem } from '../types';
import PaymentModal from '../components/PaymentModal';
import MenuManagement from '../components/MenuManagement';
import TableManagement from '../components/TableManagement';
import SimplifiedPOS from '../components/SimplifiedPOS';
import ProfessionalPOS from '../components/ProfessionalPOS';
import OrderManagement from '../components/OrderManagement';
import DashboardOverview from '../components/DashboardOverview';
import Analytics from './Analytics';
import Settings from './Settings';
import LicenseValidator from '../components/licensing/LicenseValidator';
import FeatureGate from '../components/licensing/FeatureGate';
import { useLicensing } from '../hooks/useLicensing';
import Icon from '../components/Icon';
import WindowControls from '../components/WindowControls';
import { MenuProvider } from '../contexts/MenuContext';
import { DashboardProvider } from '../contexts/DashboardContext';
import { NotificationProvider } from '../contexts/NotificationContext';
import NotificationBell from '../components/notifications/NotificationBell';
import ToastContainer from '../components/notifications/ToastContainer';
import { eventBus } from '../utils/eventBus';

interface DashboardProps {
  userDetails: UserDetails;
  restaurantDetails: RestaurantDetails;
  onLogout?: () => void;
}

type DashboardView = 'overview' | 'pos' | 'menu' | 'tables' | 'orders' | 'analytics' | 'settings';

interface OrderItem extends MenuItem {
  quantity: number;
  subtotal: number;
}

const Dashboard: React.FC<DashboardProps> = ({ userDetails, restaurantDetails, onLogout }) => {
  const [currentView, setCurrentView] = useState<DashboardView>('overview');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [posWindows, setPosWindows] = useState<string[]>([]);
  const { subscriptionStatus, daysUntilExpiry } = useLicensing(userDetails.userId || restaurantDetails.userId);

  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    } else {
      // Fallback: reload the app to go back to login
      window.location.reload();
    }
  };

  // POS Window Management
  const openPOSWindow = async () => {
    try {
      const result = await (window.electronAPI as any).createPOSWindow(
        restaurantDetails.userId,
        userDetails,
        restaurantDetails
      );

      if (result.success) {
        setPosWindows(prev => [...prev, result.windowId]);
        console.log('POS window opened:', result.windowId);
      } else {
        console.error('Failed to open POS window:', result.error);
        alert('Failed to open POS window. Please try again.');
      }
    } catch (error) {
      console.error('Error opening POS window:', error);
      alert('Failed to open POS window. Please try again.');
    }
  };

  const handlePOSNavigation = () => {
    // Instead of navigating to POS view, open a dedicated POS window
    openPOSWindow();
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // Listen for navigation events
    const handleNavigateToOrders = () => {
      setCurrentView('orders');
    };

    eventBus.on('NAVIGATE_TO_ORDERS', handleNavigateToOrders);

    return () => {
      clearInterval(timer);
      eventBus.off('NAVIGATE_TO_ORDERS', handleNavigateToOrders);
    };
  }, []);

  // Order management functions
  const addToOrder = (item: MenuItem) => {
    setOrderItems(prevItems => {
      const existingItem = prevItems.find(orderItem => orderItem.id === item.id);

      if (existingItem) {
        return prevItems.map(orderItem =>
          orderItem.id === item.id
            ? {
                ...orderItem,
                quantity: orderItem.quantity + 1,
                subtotal: (orderItem.quantity + 1) * orderItem.price
              }
            : orderItem
        );
      } else {
        return [...prevItems, {
          ...item,
          quantity: 1,
          subtotal: item.price
        }];
      }
    });
  };

  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromOrder(itemId);
      return;
    }

    setOrderItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId
          ? {
              ...item,
              quantity,
              subtotal: quantity * item.price
            }
          : item
      )
    );
  };

  const removeFromOrder = (itemId: string) => {
    setOrderItems(prevItems => prevItems.filter(item => item.id !== itemId));
  };

  const clearOrder = () => {
    setOrderItems([]);
  };

  const processPayment = () => {
    setShowPaymentModal(true);
  };

  const handlePaymentComplete = (paymentMethod: string, amountPaid: number) => {
    // Here you would typically save the order to a database
    console.log('Payment completed:', {
      orderItems,
      paymentMethod,
      amountPaid,
      timestamp: new Date().toISOString()
    });

    // Clear the order after successful payment
    clearOrder();
    setShowPaymentModal(false);

    // Show success message or redirect
    alert('Payment processed successfully!');
  };

  // Handle navigation with special case for POS
  const handleNavigation = (viewId: string) => {
    if (viewId === 'pos') {
      handlePOSNavigation();
    } else {
      setCurrentView(viewId as DashboardView);
    }
  };

  // Filter navigation items based on restaurant type
  const getNavigationItems = () => {
    const baseItems = [
      { id: 'overview', label: 'Dashboard', icon: 'home' },
      { id: 'pos', label: 'Order Taking', icon: 'shopping-cart' },
      { id: 'menu', label: 'Menu Management', icon: 'utensils' },
      { id: 'orders', label: 'Order History', icon: 'receipt' },
      { id: 'analytics', label: 'Analytics', icon: 'chart-bar' },
      { id: 'settings', label: 'Settings', icon: 'cog' },
    ];

    // Add table management only for dine-in restaurants
    if (restaurantDetails.restaurantType === 'Dine-In') {
      baseItems.splice(3, 0, { id: 'tables', label: 'Table Management', icon: 'table' });
    }

    return baseItems;
  };

  const navigationItems = getNavigationItems();

  const renderTopBar = () => (
    <div className="dashboard-topbar">
      <div className="topbar-left">
        <h1 className="restaurant-name-header">{restaurantDetails.restaurantName}</h1>
      </div>

      <div className="topbar-right">
        <div className="current-time">
          {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString()}
        </div>

        <NotificationBell userId={userDetails.userId || restaurantDetails.userId} />

        <button
          className="new-order-btn"
          onClick={handlePOSNavigation}
          title="Open dedicated Order Taking window"
        >
          <Icon name="plus" size="sm" />
          New Order
        </button>

        <button
          className="logout-btn"
          onClick={handleLogout}
          title="Logout from POS system"
        >
          <Icon name="logout" size="sm" />
        </button>

        <WindowControls />
      </div>
    </div>
  );

  const renderOverview = () => <DashboardOverview onNavigate={handleNavigation} restaurantDetails={restaurantDetails} />;

  const renderPOS = () => (
    <SimplifiedPOS restaurantId={restaurantDetails.userId} />
  );

  const renderContent = () => {
    switch (currentView) {
      case 'overview':
        return renderOverview();
      case 'pos':
        // POS is now handled by dedicated windows, show message
        return (
          <div className="pos-redirect-message">
            <div className="pos-redirect-content">
              <Icon name="external-link" size="lg" />
              <h3>Order Taking System</h3>
              <p>The Order Taking system now opens in dedicated windows for a distraction-free experience.</p>
              <button
                className="btn btn-primary"
                onClick={handlePOSNavigation}
              >
                <Icon name="plus" size="sm" />
                Open New Order Taking Window
              </button>
            </div>
          </div>
        );
      case 'menu':
        return (
          <FeatureGate
            featureId="menu_management"
            userId={userDetails.userId || restaurantDetails.userId}
            fallback={<MenuManagement restaurantId={restaurantDetails.userId} />}
          >
            <MenuManagement restaurantId={restaurantDetails.userId} />
          </FeatureGate>
        );
      case 'tables':
        // Only show table management for dine-in restaurants
        if (restaurantDetails.restaurantType !== 'Dine-In') {
          return (
            <div className="feature-unavailable">
              <h3>Table Management Not Available</h3>
              <p>Table management is only available for dine-in restaurants.</p>
              <p>Your restaurant type is set to: <strong>{restaurantDetails.restaurantType}</strong></p>
              <button
                className="btn btn-primary"
                onClick={() => setCurrentView('settings')}
              >
                Update Restaurant Settings
              </button>
            </div>
          );
        }
        return (
          <FeatureGate
            featureId="table_management"
            userId={userDetails.userId || restaurantDetails.userId}
          >
            <TableManagement restaurantId={restaurantDetails.userId} />
          </FeatureGate>
        );
      case 'orders':
        return <OrderManagement restaurantId={restaurantDetails.userId} />;
      case 'analytics':
        return (
          <FeatureGate
            featureId="analytics"
            userId={userDetails.userId || restaurantDetails.userId}
          >
            <Analytics restaurantId={restaurantDetails.userId} />
          </FeatureGate>
        );

      case 'settings':
        return <Settings
          restaurantId={restaurantDetails.userId}
          userDetails={userDetails}
          restaurantDetails={restaurantDetails}
        />;
      default:
        return renderOverview();
    }
  };

  const calculateOrderTotal = () => {
    const subtotal = orderItems.reduce((total, item) => total + item.subtotal, 0);
    const tax = subtotal * 0.08;
    return subtotal + tax;
  };

  return (
    <LicenseValidator userId={userDetails.userId || restaurantDetails.userId}>
      <NotificationProvider userId={userDetails.userId || restaurantDetails.userId}>
        <DashboardProvider restaurantId={restaurantDetails.userId}>
          <MenuProvider restaurantId={restaurantDetails.userId}>
          <div className="dashboard">
          <div className="dashboard-sidebar">
            <div className="dashboard-header">
              <h1 className="dashboard-title">Zyka POS</h1>
              <p className="dashboard-subtitle">Restaurant Management</p>
            </div>

            <nav className="nav-menu">
              {navigationItems.map((item) => (
                <button
                  key={item.id}
                  className={`nav-item ${currentView === item.id && item.id !== 'pos' ? 'active' : ''}`}
                  onClick={() => handleNavigation(item.id)}
                >
                  <span className="nav-icon">
                    <Icon name={item.icon as any} size="sm" />
                  </span>
                  <span className="nav-text">{item.label}</span>
                </button>
              ))}
            </nav>

            <div className="sidebar-footer">
              {/* Trial/Subscription Info */}
              {subscriptionStatus && (
                <div className="subscription-info">
                  <div className={`subscription-card ${subscriptionStatus.status}`}>
                    <div className="subscription-header">
                      <span className="subscription-icon">
                        <Icon
                          name={subscriptionStatus.status === 'trial' ? 'target' :
                                subscriptionStatus.status === 'active' ? 'check-circle' : 'warning'}
                          size="sm"
                        />
                      </span>
                      <div className="subscription-plan-info">
                        <span className="subscription-plan">
                          {subscriptionStatus.status === 'trial' ? 'Free Trial' :
                           subscriptionStatus.plan?.toUpperCase() || 'No Plan'}
                        </span>
                        <span className="subscription-status">
                          {subscriptionStatus.status === 'trial' ? 'Trial Active' :
                           subscriptionStatus.status === 'active' ? 'Subscription Active' :
                           subscriptionStatus.status === 'expired' ? 'Expired' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                    <div className="subscription-details">
                      <div className="days-info">
                        <span className="days-remaining">
                          {daysUntilExpiry > 0 ? `${daysUntilExpiry} days left` : 'Expired'}
                        </span>
                        {subscriptionStatus.nextBillingDate && (
                          <span className="next-billing">
                            Next billing: {new Date(subscriptionStatus.nextBillingDate).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      {subscriptionStatus.status === 'trial' && daysUntilExpiry <= 3 && daysUntilExpiry > 0 && (
                        <button
                          className="upgrade-btn"
                          onClick={() => setCurrentView('settings')}
                        >
                          <Icon name="chevron-right" size="xs" />
                          Upgrade Now
                        </button>
                      )}
                      {subscriptionStatus.status === 'expired' && (
                        <button
                          className="renew-btn"
                          onClick={() => setCurrentView('settings')}
                        >
                          <Icon name="loading" size="xs" />
                          Renew License
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              )}


            </div>
          </div>

          <div className="dashboard-main">
            {renderTopBar()}
            <div className="dashboard-content">
              {renderContent()}
            </div>
          </div>

          <PaymentModal
            isOpen={showPaymentModal}
            total={calculateOrderTotal()}
            onClose={() => setShowPaymentModal(false)}
            onPaymentComplete={handlePaymentComplete}
          />
          </div>
          <ToastContainer />
        </MenuProvider>
      </DashboardProvider>
      </NotificationProvider>
    </LicenseValidator>
  );
};

export default Dashboard;
