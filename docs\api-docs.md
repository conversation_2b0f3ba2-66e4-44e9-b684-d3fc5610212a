# Zyka POS API Documentation

This document provides comprehensive information about the Zyka POS internal API, including IPC communication between the main and renderer processes.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Authentication & Users](#authentication--users)
3. [Restaurant Management](#restaurant-management)
4. [Menu Management](#menu-management)
5. [Table Management](#table-management)
6. [Order Management](#order-management)
7. [Analytics](#analytics)
8. [Settings](#settings)
9. [File Management](#file-management)
10. [Error Handling](#error-handling)

## 🔍 Overview

Zyka POS uses Electron's IPC (Inter-Process Communication) for communication between the main process (Node.js backend) and renderer process (React frontend). All API calls are asynchronous and return promises.

### API Structure

```typescript
// All API methods are available through window.electronAPI
const result = await window.electronAPI.methodName(parameters);

// Standard response format
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
```

### Error Handling

All API methods follow a consistent error handling pattern:
- **Success**: Returns `{ success: true, data: result }`
- **Error**: Returns `{ success: false, error: "Error message" }`

## 👤 Authentication & Users

### Get User Details

```typescript
getUserDetails(userId: string): Promise<UserDetails | null>
```

**Parameters:**
- `userId` (string): Unique user identifier

**Response:**
```typescript
interface UserDetails {
  userId: string;
  username: string;
  email: string;
  role: 'admin' | 'manager' | 'staff';
  createdAt: string;
  lastLogin: string;
}
```

**Example:**
```typescript
const user = await window.electronAPI.getUserDetails('user123');
if (user) {
  console.log(`Welcome, ${user.username}!`);
}
```

### Update User Details

```typescript
updateUserDetails(userId: string, updates: Partial<UserDetails>): Promise<APIResponse<UserDetails>>
```

## 🏪 Restaurant Management

### Get Restaurant Details

```typescript
getRestaurantDetails(restaurantId: string): Promise<RestaurantDetails | null>
```

**Response:**
```typescript
interface RestaurantDetails {
  userId: string;
  restaurantName: string;
  restaurantAddress: string;
  restaurantType: 'Dine-In' | 'Takeaway' | 'Delivery' | 'Hybrid';
  location: string;
  phone: string;
  email: string;
  website: string;
  description: string;
  gstNumber: string;
  currency: string;
  currencySymbol: string;
  timezone: string;
  taxRate: number;
  serviceCharge: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### Update Restaurant Details

```typescript
updateRestaurantDetails(restaurantId: string, updates: Partial<RestaurantDetails>): Promise<APIResponse<RestaurantDetails>>
```

## 🍽️ Menu Management

### Get Menu Items

```typescript
getMenuItems(restaurantId: string): Promise<MenuItem[]>
```

**Response:**
```typescript
interface MenuItem {
  id: string;
  restaurantId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  isActive: boolean;
  preparationTime: number;
  ingredients: string[];
  allergens: string[];
  dietaryInfo: string[];
  createdAt: string;
  updatedAt: string;
}
```

### Create Menu Item

```typescript
createMenuItem(item: Omit<MenuItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<APIResponse<MenuItem>>
```

**Example:**
```typescript
const newItem = await window.electronAPI.createMenuItem({
  restaurantId: 'rest123',
  name: 'Chicken Biryani',
  description: 'Aromatic basmati rice with tender chicken',
  price: 299,
  category: 'Main Course',
  isActive: true,
  preparationTime: 25,
  ingredients: ['Chicken', 'Basmati Rice', 'Spices'],
  allergens: [],
  dietaryInfo: ['Non-Vegetarian']
});
```

### Update Menu Item

```typescript
updateMenuItem(id: string, updates: Partial<MenuItem>): Promise<APIResponse<MenuItem>>
```

### Delete Menu Item

```typescript
deleteMenuItem(id: string): Promise<APIResponse<void>>
```

## 🪑 Table Management

### Get Tables

```typescript
getTables(restaurantId: string): Promise<Table[]>
```

**Response:**
```typescript
interface Table {
  id: string;
  restaurantId: string;
  tableNumber: string;
  area: string;
  pax: number;
  status: 'available' | 'occupied' | 'reserved' | 'maintenance';
  positionX: number;
  positionY: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}
```

### Create Table

```typescript
createTable(table: Omit<Table, 'id' | 'createdAt' | 'updatedAt'>): Promise<APIResponse<Table>>
```

### Update Table

```typescript
updateTable(id: string, updates: Partial<Table>): Promise<APIResponse<Table>>
```

### Delete Table

```typescript
deleteTable(id: string): Promise<APIResponse<void>>
```

## 📋 Order Management

### Get Orders

```typescript
getOrders(restaurantId: string): Promise<Order[]>
```

**Response:**
```typescript
interface Order {
  id: string;
  restaurantId: string;
  tableId?: string;
  orderNumber: string;
  customerName?: string;
  customerPhone?: string;
  orderType: 'dine-in' | 'takeaway' | 'delivery';
  status: 'active' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  serviceCharge: number;
  discountAmount: number;
  totalAmount: number;
  paymentMethod?: string;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  notes?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  cancelledAt?: string;
}

interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  price: number;
  quantity: number;
  specialInstructions?: string;
  subtotal: number;
}
```

### Create Order

```typescript
createOrder(order: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>): Promise<APIResponse<Order>>
```

### Update Order

```typescript
updateOrder(id: string, updates: Partial<Order>): Promise<APIResponse<Order>>
```

### Delete Order

```typescript
deleteOrder(id: string): Promise<APIResponse<void>>
```

## 📊 Analytics

### Get Analytics Data

```typescript
getAnalyticsData(restaurantId: string, period?: 'today' | 'week' | 'month'): Promise<AnalyticsData>
```

**Response:**
```typescript
interface AnalyticsData {
  overview: {
    totalTables: number;
    totalMenuItems: number;
    totalOrders: number;
    totalRevenue: number;
    activeOrders: number;
    completedOrders: number;
    cancelledOrders: number;
  };
  periodStats: {
    ordersInPeriod: number;
    revenueInPeriod: number;
    averageOrderValue: number;
    busyHours: { hour: string; orders: number }[];
  };
  weeklyStats: {
    dailyRevenue: { day: string; revenue: number }[];
    popularItems: { name: string; quantity: number; revenue: number }[];
  };
  tableStats: {
    occupancyRate: number;
    averageTurnover: number;
    mostPopularTable: string;
  };
}
```

**Example:**
```typescript
const analytics = await window.electronAPI.getAnalyticsData('rest123', 'today');
console.log(`Today's revenue: ₹${analytics.periodStats.revenueInPeriod}`);
```

## ⚙️ Settings

### Get Billing Settings

```typescript
getBillingSettings(restaurantId: string): Promise<BillingSettings | null>
```

**Response:**
```typescript
interface BillingSettings {
  id: string;
  restaurantId: string;
  header: {
    showLogo: boolean;
    logoUrl?: string;
    restaurantName: string;
    address: string;
    phone: string;
    email: string;
    website: string;
    gstNumber: string;
    customText: string;
  };
  footer: {
    thankYouMessage: string;
    termsAndConditions: string;
    customText: string;
    showQRCode: boolean;
    qrCodeData: string;
  };
  format: {
    paperSize: 'thermal_80mm' | 'thermal_58mm' | 'A4';
    fontSize: 'small' | 'medium' | 'large';
    showItemImages: boolean;
    showTaxBreakdown: boolean;
  };
  printer: {
    printerName: string;
    autoprint: boolean;
    copies: number;
  };
  updatedAt: string;
}
```

### Save Billing Settings

```typescript
saveBillingSettings(settings: Omit<BillingSettings, 'id' | 'updatedAt'>): Promise<APIResponse<BillingSettings>>
```

### Get App Settings

```typescript
getAppSettings(restaurantId: string): Promise<AppSettings | null>
```

### Save App Settings

```typescript
saveAppSettings(settings: Omit<AppSettings, 'id' | 'updatedAt'>): Promise<APIResponse<AppSettings>>
```

## 📁 File Management

### Save Image

```typescript
saveImage(imageData: string, fileName: string): Promise<APIResponse<{ filePath: string }>>
```

**Parameters:**
- `imageData` (string): Base64 encoded image data
- `fileName` (string): Desired file name

### Get Image Path

```typescript
getImagePath(relativePath: string): Promise<APIResponse<{ fullPath: string }>>
```

### Delete Image

```typescript
deleteImage(relativePath: string): Promise<APIResponse<void>>
```

## 🔧 Tax Management

### Get Tax Rates

```typescript
getTaxRates(restaurantId: string): Promise<TaxRate[]>
```

**Response:**
```typescript
interface TaxRate {
  id: string;
  restaurantId: string;
  name: string;
  rate: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### Create Tax Rate

```typescript
createTaxRate(taxRate: Omit<TaxRate, 'id' | 'createdAt' | 'updatedAt'>): Promise<APIResponse<TaxRate>>
```

### Update Tax Rate

```typescript
updateTaxRate(id: string, updates: Partial<TaxRate>): Promise<APIResponse<TaxRate>>
```

### Delete Tax Rate

```typescript
deleteTaxRate(id: string): Promise<APIResponse<void>>
```

## 🛡️ Error Handling

### Common Error Codes

- **`VALIDATION_ERROR`** - Invalid input parameters
- **`NOT_FOUND`** - Resource not found
- **`PERMISSION_DENIED`** - Insufficient permissions
- **`DATABASE_ERROR`** - Database operation failed
- **`NETWORK_ERROR`** - Network connectivity issues
- **`INTERNAL_ERROR`** - Unexpected server error

### Error Response Format

```typescript
interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}
```

### Handling Errors

```typescript
try {
  const result = await window.electronAPI.createMenuItem(itemData);
  if (result.success) {
    console.log('Item created:', result.data);
  } else {
    console.error('Failed to create item:', result.error);
    // Handle specific error cases
    switch (result.code) {
      case 'VALIDATION_ERROR':
        // Show validation errors to user
        break;
      case 'PERMISSION_DENIED':
        // Redirect to login or show permission error
        break;
      default:
        // Show generic error message
        break;
    }
  }
} catch (error) {
  console.error('Unexpected error:', error);
}
```

## 🔄 Real-time Updates

Some operations trigger real-time updates across the application:

### Event Types

- **`ORDER_CREATED`** - New order placed
- **`ORDER_UPDATED`** - Order status changed
- **`TABLE_STATUS_CHANGED`** - Table status updated
- **`MENU_ITEM_UPDATED`** - Menu item modified

### Listening to Events

```typescript
// Events are handled automatically by the application
// Custom event listeners can be added in components
useEffect(() => {
  const handleOrderUpdate = (order: Order) => {
    // Handle order update
  };
  
  // Subscribe to events through context or event bus
  eventBus.on('ORDER_UPDATED', handleOrderUpdate);
  
  return () => {
    eventBus.off('ORDER_UPDATED', handleOrderUpdate);
  };
}, []);
```

---

## 📚 Additional Resources

- [Development Guide](development.md) - Setting up development environment
- [User Guide](user-guide.md) - End-user documentation
- [Troubleshooting](troubleshooting.md) - Common issues and solutions

For technical support or API questions, contact our development <NAME_EMAIL>.
