import React, { useState, useRef } from 'react';

interface ImageUploadProps {
  currentImage?: string;
  onImageChange: (imageUrl: string) => void;
  onImageRemove: () => void;
  label?: string;
  placeholder?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  currentImage,
  onImageChange,
  onImageRemove,
  label = "Image",
  placeholder = "No image selected"
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImage || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size must be less than 5MB');
      return;
    }

    setIsUploading(true);

    try {
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Upload to electron main process
      const base64Data = await fileToBase64(file);
      const result = await window.electronAPI.saveImage(base64Data, file.name);

      if (result.success && result.filePath) {
        onImageChange(result.filePath);
      } else {
        alert('Failed to upload image: ' + result.error);
        setPreviewUrl(currentImage || null);
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image');
      setPreviewUrl(currentImage || null);
    } finally {
      setIsUploading(false);
    }
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const handleRemoveImage = async () => {
    if (currentImage) {
      try {
        await window.electronAPI.deleteImage(currentImage);
      } catch (error) {
        console.error('Error deleting image:', error);
      }
    }
    setPreviewUrl(null);
    onImageRemove();
  };

  const handleUrlInput = (url: string) => {
    if (url) {
      setPreviewUrl(url);
      onImageChange(url);
    } else {
      setPreviewUrl(null);
      onImageRemove();
    }
  };

  return (
    <div className="image-upload">
      <label className="form-label">{label}</label>

      <div className="image-upload-container">
        {previewUrl ? (
          <div className="image-preview">
            <img src={previewUrl} alt="Preview" className="preview-image" />
            <div className="image-overlay">
              <button
                type="button"
                className="change-image-btn"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                📷 Change
              </button>
              <button
                type="button"
                className="remove-image-btn"
                onClick={handleRemoveImage}
                disabled={isUploading}
              >
                🗑️ Remove
              </button>
            </div>
          </div>
        ) : (
          <div className="upload-placeholder">
            <div className="upload-icon">📷</div>
            <p>{placeholder}</p>
            <button
              type="button"
              className="upload-btn"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              {isUploading ? 'Uploading...' : 'Upload Image'}
            </button>
          </div>
        )}
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />

      <div className="url-input-section">
        <label className="form-label">Or enter image URL:</label>
        <input
          type="url"
          className="form-input"
          placeholder="https://example.com/image.jpg"
          value={previewUrl && previewUrl.startsWith('http') ? previewUrl : ''}
          onChange={(e) => handleUrlInput(e.target.value)}
        />
      </div>

      <div className="upload-help">
        <p>• Supported formats: JPG, PNG, GIF, WebP</p>
        <p>• Maximum file size: 5MB</p>
        <p>• Recommended size: 400x300 pixels</p>
      </div>
    </div>
  );
};

export default ImageUpload;
