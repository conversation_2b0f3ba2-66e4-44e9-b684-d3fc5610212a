# Project Structure

This document outlines the organization and structure of the Zyka POS codebase, following industry best practices for Electron applications.

## 📁 Root Directory Structure

```
zyka-pos/
├── 📁 assets/                    # Application assets (icons, images)
├── 📁 dist/                      # Built application files (generated)
├── 📁 docs/                      # Documentation files
├── 📁 node_modules/              # Dependencies (generated)
├── 📁 release/                   # Distribution packages (generated)
├── 📁 scripts/                   # Build and utility scripts
├── 📁 src/                       # Source code
├── 📄 .gitignore                 # Git ignore rules
├── 📄 CHANGELOG.md               # Version history and changes
├── 📄 CONTRIBUTING.md            # Contribution guidelines
├── 📄 LICENSE                    # MIT license
├── 📄 README.md                  # Project overview and setup
├── 📄 package.json               # Project configuration and dependencies
├── 📄 package-lock.json          # Dependency lock file
├── 📄 tsconfig.json              # TypeScript configuration
└── 📄 webpack.config.js          # Webpack build configuration
```

## 🔧 Source Code Structure (`src/`)

```
src/
├── 📄 main.ts                    # Electron main process entry point
├── 📄 preload.ts                 # Preload script for IPC bridge
├── 📁 renderer/                  # React frontend application
├── 📁 services/                  # Backend services and utilities
└── 📁 types/                     # Shared TypeScript type definitions
```

### Main Process Files

- **`main.ts`** - Electron main process, window management, app lifecycle
- **`preload.ts`** - Secure bridge between main and renderer processes

### Backend Services (`src/services/`)

```
services/
├── 📄 analyticsService.ts        # Analytics data processing
├── 📄 backupService.ts           # Database backup and restore
├── 📄 configService.ts           # Configuration management
├── 📄 databaseService.ts         # Database connection and management
├── 📄 ipcHandlers.ts             # IPC communication handlers
└── 📄 sqliteService.ts           # SQLite database operations
```

**Service Responsibilities:**
- **Analytics Service** - Business metrics calculation and reporting
- **Backup Service** - Data backup, restore, and migration
- **Config Service** - Application configuration management
- **Database Service** - High-level database operations
- **IPC Handlers** - Communication between main and renderer
- **SQLite Service** - Low-level database queries and schema

## 🎨 Frontend Structure (`src/renderer/`)

```
renderer/
├── 📄 App.tsx                    # Root React component
├── 📄 index.tsx                  # React application entry point
├── 📄 index.html                 # HTML template
├── 📄 global.d.ts                # Global TypeScript declarations
├── 📁 components/                # Reusable UI components
├── 📁 contexts/                  # React context providers
├── 📁 hooks/                     # Custom React hooks
├── 📁 pages/                     # Main application pages/views
├── 📁 services/                  # Frontend services
├── 📁 styles/                    # CSS styles and themes
├── 📁 types/                     # Frontend type definitions
└── 📁 utils/                     # Utility functions and helpers
```

### Components (`src/renderer/components/`)

```
components/
├── 📁 licensing/                 # License management components
│   ├── 📄 FeatureGate.tsx        # Feature access control
│   ├── 📄 LicenseValidator.tsx   # License validation UI
│   └── 📄 LicenseValidator.css   # License component styles
├── 📄 DashboardOverview.tsx      # Dashboard summary component
├── 📄 Icon.tsx                   # Icon component with type safety
├── 📄 ImageUpload.tsx            # File upload component
├── 📄 MenuManagement.tsx         # Menu CRUD operations
├── 📄 OrderManagement.tsx        # Order processing interface
├── 📄 PaymentModal.tsx           # Payment processing modal
├── 📄 POSView.tsx                # Point of sale interface
├── 📄 ProfessionalPOS.tsx        # Enhanced POS system
├── 📄 TableManagement.tsx        # Table layout and management
└── 📄 WindowControls.tsx         # Custom window controls
```

### Pages (`src/renderer/pages/`)

```
pages/
├── 📄 Analytics.tsx              # Analytics dashboard page
├── 📄 Dashboard.tsx              # Main dashboard page
└── 📁 settings/                  # Settings pages
    ├── 📄 BillingSettingsTab.tsx # Receipt and billing settings
    └── 📄 RestaurantDetailsTab.tsx # Restaurant information
```

### Contexts (`src/renderer/contexts/`)

```
contexts/
├── 📄 DashboardContext.tsx       # Dashboard state management
└── 📄 MenuContext.tsx            # Menu data context
```

### Hooks (`src/renderer/hooks/`)

```
hooks/
└── 📄 useLicensing.ts            # License management hook
```

### Services (`src/renderer/services/`)

```
services/
└── 📄 billingService.ts          # Receipt generation and printing
```

### Utilities (`src/renderer/utils/`)

```
utils/
├── 📄 eventBus.ts                # Application event system
└── 📄 settingsEventBus.ts        # Settings synchronization events
```

## 📚 Documentation Structure (`docs/`)

```
docs/
├── 📄 README.md                  # Documentation overview
├── 📄 api-docs.md                # API reference documentation
├── 📄 development.md             # Development setup guide
├── 📄 project-structure.md       # This file
├── 📄 user-guide.md              # End-user documentation
├── 📄 analytics-setup-guide.md   # Analytics configuration
├── 📄 ga4-dashboard-config.json  # Google Analytics setup
└── 📄 software-owner-analytics-guide.md # Owner analytics guide
```

## 🛠️ Configuration Files

### TypeScript Configuration (`tsconfig.json`)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020", "DOM"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "jsx": "react-jsx",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true
  }
}
```

### Webpack Configuration (`webpack.config.js`)

- **Main Process** - TypeScript compilation for Electron main
- **Renderer Process** - React + TypeScript with hot reload
- **Preload Script** - TypeScript compilation for IPC bridge

### Package Configuration (`package.json`)

- **Dependencies** - Runtime dependencies
- **DevDependencies** - Development and build tools
- **Scripts** - Build, development, and utility commands
- **Build Configuration** - Electron Builder settings

## 🗄️ Database Structure

### SQLite Database Schema

```
Database Tables:
├── users                         # User authentication
├── restaurants                   # Restaurant information
├── tables                        # Table management
├── menu_items                    # Menu items and categories
├── orders                        # Order processing
├── order_items                   # Order line items
├── tax_rates                     # Tax configuration
├── billing_settings              # Receipt settings
└── app_settings                  # Application preferences
```

## 🔄 Data Flow Architecture

### IPC Communication Flow

```
Renderer Process (React)
    ↓ (User Action)
window.electronAPI.method()
    ↓ (IPC Call)
Preload Script
    ↓ (Secure Bridge)
Main Process (Node.js)
    ↓ (IPC Handler)
Service Layer
    ↓ (Database Operation)
SQLite Database
    ↑ (Result)
Service Layer
    ↑ (Response)
Main Process
    ↑ (IPC Response)
Preload Script
    ↑ (Promise Resolution)
Renderer Process (UI Update)
```

### Component Hierarchy

```
App.tsx
├── Dashboard.tsx (Main Layout)
│   ├── DashboardOverview.tsx
│   ├── ProfessionalPOS.tsx
│   ├── MenuManagement.tsx
│   ├── TableManagement.tsx
│   ├── OrderManagement.tsx
│   ├── Analytics.tsx
│   └── Settings.tsx
│       ├── RestaurantDetailsTab.tsx
│       └── BillingSettingsTab.tsx
└── Shared Components
    ├── Icon.tsx
    ├── ImageUpload.tsx
    ├── PaymentModal.tsx
    └── WindowControls.tsx
```

## 📦 Build Process

### Development Build

1. **TypeScript Compilation** - Main process and preload script
2. **Webpack Bundle** - Renderer process with hot reload
3. **Asset Processing** - CSS and static files
4. **Electron Launch** - Start application with DevTools

### Production Build

1. **TypeScript Compilation** - Optimized for production
2. **Webpack Bundle** - Minified and optimized
3. **Asset Optimization** - Compressed images and styles
4. **Electron Builder** - Create platform-specific packages

## 🔒 Security Considerations

### Context Isolation

- **Preload Script** - Secure IPC bridge with limited API exposure
- **No Node.js Access** - Renderer process isolated from Node.js APIs
- **Type Safety** - TypeScript ensures API contract compliance

### Data Security

- **Input Validation** - All user inputs validated before database operations
- **SQL Injection Prevention** - Parameterized queries only
- **File System Access** - Limited to application directories

## 🧪 Testing Structure

### Test Organization

```
tests/ (Future)
├── unit/                         # Unit tests
├── integration/                  # Integration tests
├── e2e/                          # End-to-end tests
└── fixtures/                     # Test data and mocks
```

## 📈 Performance Considerations

### Code Splitting

- **Lazy Loading** - Pages loaded on demand
- **Component Optimization** - React.memo for expensive components
- **Bundle Analysis** - Webpack bundle analyzer for optimization

### Database Optimization

- **Indexes** - Proper indexing for frequently queried columns
- **Connection Pooling** - Efficient database connection management
- **Query Optimization** - Optimized SQL queries with proper joins

## 🔧 Development Tools

### Recommended Extensions

- **VS Code Extensions** - TypeScript, React, Prettier, ESLint
- **Debugging Tools** - Electron DevTools, React Developer Tools
- **Database Tools** - SQLite Browser for database inspection

### Code Quality

- **TypeScript** - Static type checking
- **Consistent Formatting** - Prettier configuration
- **Code Standards** - ESLint rules (future implementation)

---

This structure follows industry best practices for:
- **Separation of Concerns** - Clear boundaries between layers
- **Scalability** - Easy to add new features and components
- **Maintainability** - Well-organized and documented code
- **Security** - Secure IPC communication and data handling
- **Performance** - Optimized build process and runtime performance

For questions about the project structure, refer to the [Development Guide](development.md) or contact the development team.
