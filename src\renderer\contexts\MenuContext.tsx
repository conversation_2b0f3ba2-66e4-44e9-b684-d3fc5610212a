import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { MenuItem } from '../types';

interface MenuContextType {
  menuItems: MenuItem[];
  isLoading: boolean;
  restaurantId: string;
  refreshMenuItems: () => Promise<void>;
  addMenuItem: (menuItem: Omit<MenuItem, 'id' | 'createdAt' | 'updatedAt'>) => Promise<boolean>;
  updateMenuItem: (id: string, updates: Partial<MenuItem>) => Promise<boolean>;
  deleteMenuItem: (id: string) => Promise<boolean>;
  bulkImportMenuItems: (items: any[], restaurantId?: string) => Promise<boolean>;
}

const MenuContext = createContext<MenuContextType | undefined>(undefined);

interface MenuProviderProps {
  children: ReactNode;
  restaurantId: string;
}

export const MenuProvider: React.FC<MenuProviderProps> = ({ children, restaurantId }) => {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshMenuItems = async () => {
    try {
      setIsLoading(true);
      const items = await window.electronAPI.getMenuItems(restaurantId);
      setMenuItems(items);
    } catch (error) {
      console.error('Failed to load menu items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addMenuItem = async (menuItem: Omit<MenuItem, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const menuItemWithRestaurant = {
        ...menuItem,
        restaurantId
      };
      const result = await window.electronAPI.createMenuItem(menuItemWithRestaurant);
      if (result.success && result.menuItem) {
        setMenuItems(prev => [...prev, result.menuItem!]);
        return true;
      } else {
        console.error('Failed to create menu item:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Error creating menu item:', error);
      return false;
    }
  };

  const updateMenuItem = async (id: string, updates: Partial<MenuItem>) => {
    try {
      const result = await window.electronAPI.updateMenuItem(id, updates);
      if (result.success && result.menuItem) {
        setMenuItems(prev => 
          prev.map(item => item.id === id ? result.menuItem! : item)
        );
        return true;
      } else {
        console.error('Failed to update menu item:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Error updating menu item:', error);
      return false;
    }
  };

  const deleteMenuItem = async (id: string) => {
    try {
      const result = await window.electronAPI.deleteMenuItem(id);
      if (result.success) {
        setMenuItems(prev => prev.filter(item => item.id !== id));
        return true;
      } else {
        console.error('Failed to delete menu item:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Error deleting menu item:', error);
      return false;
    }
  };

  const bulkImportMenuItems = async (items: any[], importRestaurantId?: string) => {
    try {
      const targetRestaurantId = importRestaurantId || restaurantId;
      const result = await window.electronAPI.bulkImportMenuItems(items, targetRestaurantId);
      if (result.success) {
        await refreshMenuItems(); // Refresh to get all items with proper IDs
        return true;
      } else {
        console.error('Failed to import menu items:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Error importing menu items:', error);
      return false;
    }
  };

  useEffect(() => {
    if (restaurantId) {
      refreshMenuItems();
    }
  }, [restaurantId]);

  const value: MenuContextType = {
    menuItems,
    isLoading,
    restaurantId,
    refreshMenuItems,
    addMenuItem,
    updateMenuItem,
    deleteMenuItem,
    bulkImportMenuItems
  };

  return (
    <MenuContext.Provider value={value}>
      {children}
    </MenuContext.Provider>
  );
};

export const useMenu = () => {
  const context = useContext(MenuContext);
  if (context === undefined) {
    throw new Error('useMenu must be used within a MenuProvider');
  }
  return context;
};

export default MenuContext;
